{"_format": "hh-sol-cache-2", "files": {"/Users/<USER>/Downloads/Riwev22/contracts-deployment/contracts/RiweStaking.sol": {"lastModificationDate": 1749162213796, "contentHash": "c4a0b989dec871a7362b896d3bd0d2c3", "sourceName": "contracts/RiweStaking.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/security/ReentrancyGuard.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["RiweStaking"]}, "/Users/<USER>/Downloads/Riwev22/contracts-deployment/node_modules/@openzeppelin/contracts/security/ReentrancyGuard.sol": {"lastModificationDate": 1749157656948, "contentHash": "1535f8c0c68463f8c1b5239f7584e71f", "sourceName": "@openzeppelin/contracts/security/ReentrancyGuard.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Reentrancy<PERSON><PERSON>"]}, "/Users/<USER>/Downloads/Riwev22/contracts-deployment/node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"lastModificationDate": 1749157656899, "contentHash": "df36f7051335cd1e748b1b6463b7fdd3", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC20"]}, "/Users/<USER>/Downloads/Riwev22/contracts-deployment/node_modules/@openzeppelin/contracts/access/Ownable.sol": {"lastModificationDate": 1749157656941, "contentHash": "5a20b2cad87ddb61c7a3a6af21289e28", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["Ownable"]}, "/Users/<USER>/Downloads/Riwev22/contracts-deployment/node_modules/@openzeppelin/contracts/utils/Context.sol": {"lastModificationDate": 1749157656568, "contentHash": "5f2c5c4b6af2dd4551027144797bc8be", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Context"]}, "/Users/<USER>/Downloads/Riwev22/contracts-deployment/node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"lastModificationDate": 1749157656620, "contentHash": "********************************", "sourceName": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20.sol", "./extensions/IERC20Metadata.sol", "../../utils/Context.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ERC20"]}, "/Users/<USER>/Downloads/Riwev22/contracts-deployment/node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"lastModificationDate": 1749157656900, "contentHash": "909ab67fc5c25033fe6cd364f8c056f9", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC20.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC20Metadata"]}, "/Users/<USER>/Downloads/Riwev22/contracts-deployment/contracts/RiweToken.sol": {"lastModificationDate": 1749157614018, "contentHash": "72ef8c611b8bdd8881f0c757156893db", "sourceName": "contracts/RiweToken.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/access/Ownable.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["RiweToken"]}}}