const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Checking Polygon Amoy wallet balance...");
  console.log("==========================================");
  
  const [deployer] = await ethers.getSigners();
  const balance = await ethers.provider.getBalance(deployer.address);
  
  console.log("👤 Wallet Address:", deployer.address);
  console.log("💰 Balance:", ethers.formatEther(balance), "POL");
  console.log("🌐 Network:", network.name);
  console.log("🔗 Chain ID:", network.config.chainId);
  
  if (balance < ethers.parseEther("0.1")) {
    console.log("\n⚠️  WARNING: You need more POL for deployment!");
    console.log("📍 Get test POL from Polygon Amoy faucet:");
    console.log("   - https://faucet.polygon.technology/ (select Amoy)");
    console.log("   - https://www.alchemy.com/faucets/polygon-amoy");
    console.log("💡 You need at least 0.1 POL for deployment");
    console.log("📋 Your wallet address: " + deployer.address);
  } else {
    console.log("\n✅ You have enough POL for deployment!");
    console.log("🚀 Ready to deploy your smart contracts!");
  }
  
  console.log("\n🔗 View your wallet on Amoy PolygonScan:");
  console.log("   https://amoy.polygonscan.com/address/" + deployer.address);
}

main().catch((error) => {
  console.error("❌ Error checking balance:", error.message);
  process.exit(1);
});
