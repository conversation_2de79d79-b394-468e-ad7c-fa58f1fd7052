{"_format": "hh-sol-artifact-1", "contractName": "RiweStaking", "sourceName": "contracts/RiweStaking.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_riweToken", "type": "address"}, {"internalType": "uint256", "name": "_rewardRate", "type": "uint256"}, {"internalType": "uint256", "name": "_minimumStakeAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_minimumStakingPeriod", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "MinimumStakeAmountUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newPeriod", "type": "uint256"}], "name": "MinimumStakingPeriodUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "reward", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>laimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newRate", "type": "uint256"}], "name": "RewardRateUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Staked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Unstaked", "type": "event"}, {"inputs": [], "name": "claimReward", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "earned", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "exit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getContractStats", "outputs": [{"internalType": "uint256", "name": "_totalStaked", "type": "uint256"}, {"internalType": "uint256", "name": "_rewardRate", "type": "uint256"}, {"internalType": "uint256", "name": "_minimumStakeAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_minimumStakingPeriod", "type": "uint256"}, {"internalType": "uint256", "name": "contractBalance", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserStakeInfo", "outputs": [{"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint256", "name": "earnedRewards", "type": "uint256"}, {"internalType": "uint256", "name": "lastStakeTime", "type": "uint256"}, {"internalType": "uint256", "name": "totalRewardsClaimed", "type": "uint256"}, {"internalType": "bool", "name": "canUnstake", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastUpdateTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minimumStakeAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minimumStakingPeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rewardPerToken", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardPerTokenStored", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "rewards", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "riweToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "stake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "stakes", "outputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "rewardDebt", "type": "uint256"}, {"internalType": "uint256", "name": "lastStakeTime", "type": "uint256"}, {"internalType": "uint256", "name": "totalRewardsClaimed", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalStaked", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "unstake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_minimumStakeAmount", "type": "uint256"}], "name": "updateMinimumStakeAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_minimumStakingPeriod", "type": "uint256"}], "name": "updateMinimumStakingPeriod", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_rewardRate", "type": "uint256"}], "name": "updateRewardRate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "userRewardPerTokenPaid", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}