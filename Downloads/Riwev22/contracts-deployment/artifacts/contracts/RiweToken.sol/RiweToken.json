{"_format": "hh-sol-artifact-1", "contractName": "RiweToken", "sourceName": "contracts/RiweToken.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "_totalSupply", "type": "uint256"}, {"internalType": "uint256", "name": "_maxSupply", "type": "uint256"}, {"internalType": "uint8", "name": "_tokenDecimals", "type": "uint8"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newMaxSupply", "type": "uint256"}], "name": "MaxSupplyUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensBurned", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensMinted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "recipients", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}], "name": "batchTransfer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burnFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getTokenInfo", "outputs": [{"internalType": "string", "name": "tokenName", "type": "string"}, {"internalType": "string", "name": "tokenSymbol", "type": "string"}, {"internalType": "uint8", "name": "tokenDecimals", "type": "uint8"}, {"internalType": "uint256", "name": "tokenTotalSupply", "type": "uint256"}, {"internalType": "uint256", "name": "tokenMaxSupply", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "maxSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_maxSupply", "type": "uint256"}], "name": "updateMaxSupply", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}