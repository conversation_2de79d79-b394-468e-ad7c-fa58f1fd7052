const { ethers } = require("hardhat");
const fs = require('fs');

async function main() {
  console.log("🌾 Deploying Riwev22 Smart Contracts...");
  console.log("=====================================");
  
  const [deployer] = await ethers.getSigners();
  console.log("📍 Deploying contracts with account:", deployer.address);
  
  const balance = await ethers.provider.getBalance(deployer.address);
  console.log("💰 Account balance:", ethers.formatEther(balance), "POL");

  if (balance < ethers.parseEther("0.1")) {
    console.log("⚠️  WARNING: Low balance! You need more POL for deployment");
  }

  console.log("🌐 Network:", network.name);
  console.log("");

  // Deploy RIWE Token
  console.log("📄 Deploying RIWE Token...");
  const RiweToken = await ethers.getContractFactory("RiweToken");
  
  const riweToken = await RiweToken.deploy(
    process.env.RIWE_TOKEN_NAME || "RIWE Token",
    process.env.RIWE_TOKEN_SYMBOL || "RIWE",
    process.env.RIWE_TOKEN_TOTAL_SUPPLY || "1000000000000000000000000000", // 1B tokens
    process.env.RIWE_TOKEN_MAX_SUPPLY || "10000000000000000000000000000",   // 10B tokens
    process.env.RIWE_TOKEN_DECIMALS || 18
  );
  
  console.log("⏳ Waiting for RIWE Token deployment...");
  await riweToken.deployed();
  console.log("✅ RIWE Token deployed to:", riweToken.address);
  console.log("🔗 Transaction hash:", riweToken.deployTransaction.hash);

  // Wait for a few confirmations
  console.log("⏳ Waiting for confirmations...");
  await riweToken.deployTransaction.wait(2);

  // Deploy Staking Contract
  console.log("\n💰 Deploying Staking Contract...");
  const RiweStaking = await ethers.getContractFactory("RiweStaking");
  
  const riweStaking = await RiweStaking.deploy(
    riweToken.address,
    process.env.STAKING_REWARD_RATE || "3170979198376458", // ~10% APY
    process.env.STAKING_MIN_AMOUNT || "100000000000000000000", // 100 tokens
    process.env.STAKING_MIN_PERIOD || "604800" // 7 days
  );
  
  console.log("⏳ Waiting for Staking Contract deployment...");
  await riweStaking.deployed();
  console.log("✅ Staking Contract deployed to:", riweStaking.address);
  console.log("🔗 Transaction hash:", riweStaking.deployTransaction.hash);

  // Wait for confirmations
  await riweStaking.deployTransaction.wait(2);

  // Transfer tokens to staking contract for rewards
  console.log("\n💸 Setting up staking rewards...");
  const rewardAmount = ethers.parseEther("10000000"); // 10M tokens for rewards

  console.log("⏳ Transferring", ethers.formatEther(rewardAmount), "RIWE to staking contract...");
  const transferTx = await riweToken.transfer(riweStaking.address, rewardAmount);
  await transferTx.wait(2);
  console.log("✅ Reward tokens transferred successfully");

  // Verify token info
  console.log("\n📊 Verifying deployment...");
  const tokenName = await riweToken.name();
  const tokenSymbol = await riweToken.symbol();
  const totalSupply = await riweToken.totalSupply();
  const stakingBalance = await riweToken.balanceOf(riweStaking.address);
  
  console.log("📄 Token Name:", tokenName);
  console.log("🏷️  Token Symbol:", tokenSymbol);
  console.log("📈 Total Supply:", ethers.formatEther(totalSupply), "RIWE");
  console.log("💰 Staking Contract Balance:", ethers.formatEther(stakingBalance), "RIWE");

  // Create deployment summary
  const deploymentInfo = {
    network: network.name,
    chainId: network.config.chainId,
    deployer: deployer.address,
    deployerBalance: ethers.formatEther(balance),
    timestamp: new Date().toISOString(),
    contracts: {
      RiweToken: {
        address: riweToken.address,
        transactionHash: riweToken.deployTransaction.hash,
        name: tokenName,
        symbol: tokenSymbol,
        totalSupply: ethers.formatEther(totalSupply)
      },
      RiweStaking: {
        address: riweStaking.address,
        transactionHash: riweStaking.deployTransaction.hash,
        rewardBalance: ethers.formatEther(stakingBalance)
      }
    },
    gasUsed: {
      riweToken: riweToken.deployTransaction.gasLimit?.toString(),
      riweStaking: riweStaking.deployTransaction.gasLimit?.toString()
    }
  };
  
  // Save deployment info
  const filename = `deployment-${network.name}-${Date.now()}.json`;
  fs.writeFileSync(filename, JSON.stringify(deploymentInfo, null, 2));
  
  console.log("\n🎉 DEPLOYMENT COMPLETE!");
  console.log("======================");
  console.log("📄 RIWE Token:", riweToken.address);
  console.log("💰 Staking Contract:", riweStaking.address);
  console.log("🌐 Network:", network.name);
  console.log("👤 Deployer:", deployer.address);
  console.log("📋 Deployment info saved to:", filename);
  
  console.log("\n🔧 NEXT STEPS:");
  console.log("1. Update your Laravel .env file with these contract addresses:");
  console.log(`   RIWE_TOKEN_CONTRACT_ADDRESS=${riweToken.address}`);
  console.log(`   STAKING_CONTRACT_ADDRESS=${riweStaking.address}`);
  console.log("");
  console.log("2. Verify contracts on block explorer:");
  console.log(`   npx hardhat verify --network ${network.name} ${riweToken.address} "${tokenName}" "${tokenSymbol}" "${process.env.RIWE_TOKEN_TOTAL_SUPPLY}" "${process.env.RIWE_TOKEN_MAX_SUPPLY}" ${process.env.RIWE_TOKEN_DECIMALS}`);
  console.log("");
  console.log("3. Test your contracts:");
  console.log(`   npx hardhat console --network ${network.name}`);
  
  if (network.name.includes('mumbai')) {
    console.log("\n🔗 View on PolygonScan Mumbai:");
    console.log(`   Token: https://mumbai.polygonscan.com/address/${riweToken.address}`);
    console.log(`   Staking: https://mumbai.polygonscan.com/address/${riweStaking.address}`);
  } else if (network.name.includes('polygon')) {
    console.log("\n🔗 View on PolygonScan:");
    console.log(`   Token: https://polygonscan.com/address/${riweToken.address}`);
    console.log(`   Staking: https://polygonscan.com/address/${riweStaking.address}`);
  }
  
  console.log("\n🌾 Your blockchain-powered agricultural platform is ready! 🚀");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
