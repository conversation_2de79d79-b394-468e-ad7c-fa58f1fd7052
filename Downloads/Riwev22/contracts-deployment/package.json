{"name": "contracts-deployment", "version": "1.0.0", "main": "index.js", "scripts": {"setup": "./setup-env.sh", "deploy:amoy": "hardhat run scripts/deploy.js --network polygon_amoy", "deploy:testnet": "hardhat run scripts/deploy.js --network polygon_mumbai", "deploy:mainnet": "hardhat run scripts/deploy.js --network polygon", "verify:amoy": "hardhat verify --network polygon_amoy", "verify:testnet": "hardhat verify --network polygon_mumbai", "verify:mainnet": "hardhat verify --network polygon", "console:amoy": "hardhat console --network polygon_amoy", "console:testnet": "hardhat console --network polygon_mumbai", "console:mainnet": "hardhat console --network polygon", "test": "hardhat test"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^5.0.0", "@openzeppelin/contracts": "^4.9.0", "dotenv": "^16.5.0", "hardhat": "^2.24.2"}}