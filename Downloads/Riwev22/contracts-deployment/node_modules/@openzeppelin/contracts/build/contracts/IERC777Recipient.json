{"_format": "hh-sol-artifact-1", "contractName": "IERC777Recipient", "sourceName": "contracts/token/ERC777/IERC777Recipient.sol", "abi": [{"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "userData", "type": "bytes"}, {"internalType": "bytes", "name": "operatorData", "type": "bytes"}], "name": "tokensReceived", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}