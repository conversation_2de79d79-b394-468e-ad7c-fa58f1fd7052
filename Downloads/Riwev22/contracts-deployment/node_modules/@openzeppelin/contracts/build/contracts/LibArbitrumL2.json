{"_format": "hh-sol-artifact-1", "contractName": "LibArbitrumL2", "sourceName": "contracts/crosschain/arbitrum/LibArbitrumL2.sol", "abi": [{"inputs": [], "name": "ARBSYS", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x6091610038600b82828239805160001a607314602b57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe730000000000000000000000000000000000000000301460806040526004361060335760003560e01c8063bf0a12cf146038575b600080fd5b603f606481565b6040516001600160a01b03909116815260200160405180910390f3fea2646970667358221220a997e9a9bed247ae5a204c5becb72b696557d3f8c1958c819328313f56d94d9f64736f6c634300080d0033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040526004361060335760003560e01c8063bf0a12cf146038575b600080fd5b603f606481565b6040516001600160a01b03909116815260200160405180910390f3fea2646970667358221220a997e9a9bed247ae5a204c5becb72b696557d3f8c1958c819328313f56d94d9f64736f6c634300080d0033", "linkReferences": {}, "deployedLinkReferences": {}}