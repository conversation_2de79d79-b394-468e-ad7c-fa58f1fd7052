// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

/**
 * @title RiweStaking
 * @dev Staking contract for RIWE tokens with rewards
 * Features:
 * - Stake RIWE tokens to earn rewards
 * - Configurable reward rates
 * - Emergency pause functionality
 * - Minimum staking period
 */
contract RiweStaking is Ownable, ReentrancyGuard {
    IERC20 public riweToken;
    
    // Staking parameters
    uint256 public rewardRate; // Reward rate per second (in wei)
    uint256 public minimumStakeAmount;
    uint256 public minimumStakingPeriod; // In seconds
    uint256 public totalStaked;
    
    // User staking information
    struct StakeInfo {
        uint256 amount;
        uint256 rewardDebt;
        uint256 lastStakeTime;
        uint256 totalRewardsClaimed;
    }
    
    mapping(address => StakeInfo) public stakes;
    mapping(address => uint256) public userRewardPerTokenPaid;
    mapping(address => uint256) public rewards;
    
    uint256 public lastUpdateTime;
    uint256 public rewardPerTokenStored;
    
    // Events
    event Staked(address indexed user, uint256 amount);
    event Unstaked(address indexed user, uint256 amount);
    event RewardClaimed(address indexed user, uint256 reward);
    event RewardRateUpdated(uint256 newRate);
    event MinimumStakeAmountUpdated(uint256 newAmount);
    event MinimumStakingPeriodUpdated(uint256 newPeriod);
    
    constructor(
        address _riweToken,
        uint256 _rewardRate,
        uint256 _minimumStakeAmount,
        uint256 _minimumStakingPeriod
    ) {
        riweToken = IERC20(_riweToken);
        rewardRate = _rewardRate;
        minimumStakeAmount = _minimumStakeAmount;
        minimumStakingPeriod = _minimumStakingPeriod;
        lastUpdateTime = block.timestamp;
    }
    
    modifier updateReward(address account) {
        rewardPerTokenStored = rewardPerToken();
        lastUpdateTime = block.timestamp;
        
        if (account != address(0)) {
            rewards[account] = earned(account);
            userRewardPerTokenPaid[account] = rewardPerTokenStored;
        }
        _;
    }
    
    /**
     * @dev Calculate reward per token
     */
    function rewardPerToken() public view returns (uint256) {
        if (totalStaked == 0) {
            return rewardPerTokenStored;
        }
        
        return rewardPerTokenStored + 
            (((block.timestamp - lastUpdateTime) * rewardRate * 1e18) / totalStaked);
    }
    
    /**
     * @dev Calculate earned rewards for an account
     */
    function earned(address account) public view returns (uint256) {
        return ((stakes[account].amount * 
            (rewardPerToken() - userRewardPerTokenPaid[account])) / 1e18) + 
            rewards[account];
    }
    
    /**
     * @dev Stake RIWE tokens
     * @param amount Amount of tokens to stake
     */
    function stake(uint256 amount) external nonReentrant updateReward(msg.sender) {
        require(amount >= minimumStakeAmount, "RiweStaking: amount below minimum");
        require(amount > 0, "RiweStaking: cannot stake 0");
        
        // Transfer tokens from user to contract
        riweToken.transferFrom(msg.sender, address(this), amount);
        
        // Update user stake info
        stakes[msg.sender].amount += amount;
        stakes[msg.sender].lastStakeTime = block.timestamp;
        totalStaked += amount;
        
        emit Staked(msg.sender, amount);
    }
    
    /**
     * @dev Unstake RIWE tokens
     * @param amount Amount of tokens to unstake
     */
    function unstake(uint256 amount) external nonReentrant updateReward(msg.sender) {
        require(amount > 0, "RiweStaking: cannot unstake 0");
        require(stakes[msg.sender].amount >= amount, "RiweStaking: insufficient staked amount");
        require(
            block.timestamp >= stakes[msg.sender].lastStakeTime + minimumStakingPeriod,
            "RiweStaking: minimum staking period not met"
        );
        
        // Update user stake info
        stakes[msg.sender].amount -= amount;
        totalStaked -= amount;
        
        // Transfer tokens back to user
        riweToken.transfer(msg.sender, amount);
        
        emit Unstaked(msg.sender, amount);
    }
    
    /**
     * @dev Claim accumulated rewards
     */
    function claimReward() external nonReentrant updateReward(msg.sender) {
        uint256 reward = rewards[msg.sender];
        require(reward > 0, "RiweStaking: no rewards to claim");
        
        rewards[msg.sender] = 0;
        stakes[msg.sender].totalRewardsClaimed += reward;
        
        // Transfer reward tokens to user
        riweToken.transfer(msg.sender, reward);
        
        emit RewardClaimed(msg.sender, reward);
    }
    
    /**
     * @dev Exit staking (unstake all and claim rewards)
     */
    function exit() external nonReentrant updateReward(msg.sender) {
        uint256 stakedAmount = stakes[msg.sender].amount;
        require(stakedAmount > 0, "RiweStaking: no tokens staked");
        require(
            block.timestamp >= stakes[msg.sender].lastStakeTime + minimumStakingPeriod,
            "RiweStaking: minimum staking period not met"
        );

        // Unstake all tokens
        totalStaked -= stakedAmount;
        stakes[msg.sender].amount = 0;

        // Claim rewards
        uint256 reward = rewards[msg.sender];
        if (reward > 0) {
            rewards[msg.sender] = 0;
            stakes[msg.sender].totalRewardsClaimed += reward;
        }

        // Transfer tokens back to user
        riweToken.transfer(msg.sender, stakedAmount);

        // Transfer rewards if any
        if (reward > 0) {
            riweToken.transfer(msg.sender, reward);
            emit RewardClaimed(msg.sender, reward);
        }

        emit Unstaked(msg.sender, stakedAmount);
    }
    
    /**
     * @dev Get user staking information
     */
    function getUserStakeInfo(address user) external view returns (
        uint256 stakedAmount,
        uint256 earnedRewards,
        uint256 lastStakeTime,
        uint256 totalRewardsClaimed,
        bool canUnstake
    ) {
        StakeInfo memory userStake = stakes[user];
        return (
            userStake.amount,
            earned(user),
            userStake.lastStakeTime,
            userStake.totalRewardsClaimed,
            block.timestamp >= userStake.lastStakeTime + minimumStakingPeriod
        );
    }
    
    /**
     * @dev Update reward rate (only owner)
     */
    function updateRewardRate(uint256 _rewardRate) external onlyOwner updateReward(address(0)) {
        rewardRate = _rewardRate;
        emit RewardRateUpdated(_rewardRate);
    }
    
    /**
     * @dev Update minimum stake amount (only owner)
     */
    function updateMinimumStakeAmount(uint256 _minimumStakeAmount) external onlyOwner {
        minimumStakeAmount = _minimumStakeAmount;
        emit MinimumStakeAmountUpdated(_minimumStakeAmount);
    }
    
    /**
     * @dev Update minimum staking period (only owner)
     */
    function updateMinimumStakingPeriod(uint256 _minimumStakingPeriod) external onlyOwner {
        minimumStakingPeriod = _minimumStakingPeriod;
        emit MinimumStakingPeriodUpdated(_minimumStakingPeriod);
    }
    

    
    /**
     * @dev Emergency withdraw function (only owner)
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        if (token == address(0)) {
            payable(owner()).transfer(amount);
        } else {
            IERC20(token).transfer(owner(), amount);
        }
    }
    
    /**
     * @dev Get contract statistics
     */
    function getContractStats() external view returns (
        uint256 _totalStaked,
        uint256 _rewardRate,
        uint256 _minimumStakeAmount,
        uint256 _minimumStakingPeriod,
        uint256 contractBalance
    ) {
        return (
            totalStaked,
            rewardRate,
            minimumStakeAmount,
            minimumStakingPeriod,
            riweToken.balanceOf(address(this))
        );
    }
}
