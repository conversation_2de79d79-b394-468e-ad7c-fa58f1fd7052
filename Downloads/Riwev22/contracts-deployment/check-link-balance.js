const { ethers } = require("hardhat");

async function main() {
  console.log("🔗 Checking LINK token balance on Polygon Amoy...");
  console.log("=================================================");
  
  const [deployer] = await ethers.getSigners();
  
  // LINK token contract address on Polygon Amoy
  const LINK_TOKEN_ADDRESS = "******************************************";
  
  // ERC20 ABI for balance checking
  const ERC20_ABI = [
    "function balanceOf(address owner) view returns (uint256)",
    "function decimals() view returns (uint8)",
    "function symbol() view returns (string)"
  ];
  
  try {
    const linkToken = new ethers.Contract(LINK_TOKEN_ADDRESS, ERC20_ABI, ethers.provider);
    
    const balance = await linkToken.balanceOf(deployer.address);
    const decimals = await linkToken.decimals();
    const symbol = await linkToken.symbol();
    
    const formattedBalance = ethers.formatUnits(balance, decimals);
    
    console.log("👤 Wallet Address:", deployer.address);
    console.log("🔗 LINK Balance:", formattedBalance, symbol);
    console.log("🌐 Network:", network.name);
    console.log("🔗 Chain ID:", network.config.chainId);
    
    // Check native POL balance too
    const nativeBalance = await ethers.provider.getBalance(deployer.address);
    console.log("💰 Native POL Balance:", ethers.formatEther(nativeBalance), "POL");
    
    if (nativeBalance < ethers.parseEther("0.01")) {
      console.log("\n⚠️  WARNING: You need POL for gas fees!");
      console.log("💡 LINK tokens can't pay for gas - you need native POL");
      console.log("📍 Get test POL from: https://faucet.polygon.technology/ (select Amoy)");
      console.log("💡 You need at least 0.01 POL for gas fees");
    } else {
      console.log("\n✅ You have enough POL for gas fees!");
    }
    
    if (parseFloat(formattedBalance) > 0) {
      console.log("✅ You have LINK tokens!");
      console.log("💡 Note: LINK tokens are ERC-20 tokens, not native currency");
      console.log("💡 You still need POL for gas fees to deploy contracts");
    } else {
      console.log("❌ No LINK tokens found");
    }
    
    console.log("\n🔗 View your wallet on Amoy PolygonScan:");
    console.log("   https://amoy.polygonscan.com/address/" + deployer.address);
    
  } catch (error) {
    console.error("❌ Error checking LINK balance:", error.message);
    console.log("💡 This might be normal if LINK contract address is different");
  }
}

main().catch((error) => {
  console.error("❌ Error:", error.message);
  process.exit(1);
});
