<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="test-token">
    <title>Tailwind CSS Test</title>
    
    <!-- Load the built CSS -->
    <link rel="stylesheet" href="/build/assets/app-CdktGiDo.css">
    
    <style>
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body class="p-8 bg-gray-100">
    <h1 class="text-3xl font-bold text-gray-900 mb-8">Tailwind CSS Test</h1>
    
    <!-- Visual Tests -->
    <div class="space-y-6">
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-xl font-semibold mb-4">Visual Tests</h2>
            
            <div class="bg-blue-500 text-white p-4 rounded-lg mb-4">
                <p class="font-bold">Blue Background Test</p>
                <p class="text-sm opacity-90">This should have a blue background with white text</p>
            </div>
            
            <div class="bg-red-500 text-white p-4 rounded-lg mb-4">
                <p class="font-bold">Red Background Test</p>
                <p class="text-sm opacity-90">This should have a red background with white text</p>
            </div>
            
            <div class="bg-green-500 text-white p-4 rounded-lg mb-4">
                <p class="font-bold">Green Background Test</p>
                <p class="text-sm opacity-90">This should have a green background with white text</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-purple-100 p-4 rounded text-center">Grid Item 1</div>
                <div class="bg-yellow-100 p-4 rounded text-center">Grid Item 2</div>
                <div class="bg-pink-100 p-4 rounded text-center">Grid Item 3</div>
            </div>
        </div>
        
        <!-- Responsive Test -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-xl font-semibold mb-4">Responsive Test</h2>
            <div class="block md:hidden bg-red-100 p-4 rounded">
                <p>This shows on mobile only</p>
            </div>
            <div class="hidden md:block bg-green-100 p-4 rounded">
                <p>This shows on desktop only</p>
            </div>
        </div>
        
        <!-- JavaScript Test Results -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-xl font-semibold mb-4">JavaScript Test Results</h2>
            <div id="test-results"></div>
        </div>
    </div>

    <!-- Load JS assets -->
    <script type="module" src="/build/assets/app-D58IS0fg.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const results = document.getElementById('test-results');
            
            // Test 1: Check if Tailwind classes are applied
            const testElement = document.createElement('div');
            testElement.className = 'bg-blue-500 p-4 text-white';
            document.body.appendChild(testElement);
            
            const styles = window.getComputedStyle(testElement);
            const bgColor = styles.backgroundColor;
            const padding = styles.padding;
            const color = styles.color;
            
            // More flexible color checking
            const hasBlueBackground = bgColor.includes('59, 130, 246') || // Tailwind blue-500
                                    bgColor.includes('rgb(59, 130, 246)') ||
                                    bgColor.includes('rgba(59, 130, 246') ||
                                    bgColor === 'rgb(59, 130, 246)';
            
            const hasPadding = padding !== '0px' && padding !== '';
            const hasWhiteText = color.includes('255, 255, 255') || 
                               color.includes('rgb(255, 255, 255)') ||
                               color === 'rgb(255, 255, 255)';
            
            document.body.removeChild(testElement);
            
            // Display results
            if (hasBlueBackground) {
                results.innerHTML += '<div class="test-result success">✅ Background colors working</div>';
            } else {
                results.innerHTML += `<div class="test-result error">❌ Background colors not working (got: ${bgColor})</div>`;
            }
            
            if (hasPadding) {
                results.innerHTML += '<div class="test-result success">✅ Padding classes working</div>';
            } else {
                results.innerHTML += `<div class="test-result error">❌ Padding classes not working (got: ${padding})</div>`;
            }
            
            if (hasWhiteText) {
                results.innerHTML += '<div class="test-result success">✅ Text colors working</div>';
            } else {
                results.innerHTML += `<div class="test-result error">❌ Text colors not working (got: ${color})</div>`;
            }
            
            // Test 2: Check Alpine.js
            setTimeout(() => {
                if (typeof window.Alpine !== 'undefined') {
                    results.innerHTML += '<div class="test-result success">✅ Alpine.js loaded</div>';
                } else {
                    results.innerHTML += '<div class="test-result error">❌ Alpine.js not loaded</div>';
                }
            }, 1000);
            
            // Test 3: Check Axios
            if (typeof window.axios !== 'undefined') {
                results.innerHTML += '<div class="test-result success">✅ Axios loaded</div>';
            } else {
                results.innerHTML += '<div class="test-result error">❌ Axios not loaded</div>';
            }
            
            // Test 4: CSS file loading
            const stylesheets = Array.from(document.styleSheets);
            const tailwindLoaded = stylesheets.some(sheet => {
                try {
                    return sheet.href && sheet.href.includes('app-CdktGiDo.css');
                } catch (e) {
                    return false;
                }
            });
            
            if (tailwindLoaded) {
                results.innerHTML += '<div class="test-result success">✅ Tailwind CSS file loaded</div>';
            } else {
                results.innerHTML += '<div class="test-result error">❌ Tailwind CSS file not loaded</div>';
            }
            
            // Test 5: Check for CSS rules
            let tailwindRulesFound = false;
            try {
                stylesheets.forEach(sheet => {
                    if (sheet.cssRules) {
                        for (let rule of sheet.cssRules) {
                            if (rule.selectorText && (
                                rule.selectorText.includes('.bg-blue-500') ||
                                rule.selectorText.includes('.text-white') ||
                                rule.selectorText.includes('.p-4')
                            )) {
                                tailwindRulesFound = true;
                                break;
                            }
                        }
                    }
                });
            } catch (e) {
                console.warn('Could not check CSS rules:', e);
            }
            
            if (tailwindRulesFound) {
                results.innerHTML += '<div class="test-result success">✅ Tailwind CSS rules found</div>';
            } else {
                results.innerHTML += '<div class="test-result error">❌ Tailwind CSS rules not found</div>';
            }
            
            console.log('Tailwind Test Results:', {
                backgroundColor: bgColor,
                padding: padding,
                color: color,
                hasBlueBackground,
                hasPadding,
                hasWhiteText,
                stylesheetCount: stylesheets.length
            });
        });
    </script>
</body>
</html>
