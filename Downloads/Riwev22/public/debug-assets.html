<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Assets</title>
    
    <!-- Load the actual assets -->
    <link rel="stylesheet" href="/build/assets/app-CdktGiDo.css">
    <link rel="stylesheet" href="/css/emergency-fix.css">
    
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .warning { color: #f59e0b; }
    </style>
</head>
<body>
    <h1>Asset Loading Debug</h1>
    
    <div class="debug-panel">
        <h2>CSS Loading Test</h2>
        <div id="css-results"></div>
    </div>
    
    <div class="debug-panel">
        <h2>Tailwind CSS Test</h2>
        <div class="bg-blue-500 text-white p-4 rounded">
            If you can see this styled box, Tailwind CSS is working!
        </div>
    </div>
    
    <div class="debug-panel">
        <h2>Emergency CSS Test</h2>
        <div class="sidenav" style="position: relative; width: 200px; height: 100px; background: #f0f0f0;">
            Sidebar Test (should have emergency styles)
        </div>
    </div>
    
    <div class="debug-panel">
        <h2>JavaScript Test</h2>
        <div id="js-results"></div>
        <button onclick="testJS()">Test JavaScript</button>
    </div>

    <!-- Load the JS assets -->
    <script type="module" src="/build/assets/app-D58IS0fg.js"></script>
    
    <script>
        // Test CSS loading
        function checkCSS() {
            const results = document.getElementById('css-results');
            const links = document.querySelectorAll('link[rel="stylesheet"]');
            
            results.innerHTML = '<h3>Loaded CSS Files:</h3>';
            
            links.forEach((link, index) => {
                const url = link.href;
                const status = link.sheet ? 'Loaded' : 'Failed';
                const className = link.sheet ? 'success' : 'error';
                
                results.innerHTML += `
                    <div class="${className}">
                        ${index + 1}. ${url} - ${status}
                    </div>
                `;
            });
        }
        
        function testJS() {
            const results = document.getElementById('js-results');
            
            try {
                // Test if Alpine.js is loaded
                if (typeof Alpine !== 'undefined') {
                    results.innerHTML += '<div class="success">✓ Alpine.js is loaded</div>';
                } else {
                    results.innerHTML += '<div class="warning">⚠ Alpine.js not found</div>';
                }
                
                // Test if axios is loaded
                if (typeof axios !== 'undefined') {
                    results.innerHTML += '<div class="success">✓ Axios is loaded</div>';
                } else {
                    results.innerHTML += '<div class="warning">⚠ Axios not found</div>';
                }
                
                // Test basic JS functionality
                results.innerHTML += '<div class="success">✓ JavaScript is working</div>';
                
            } catch (error) {
                results.innerHTML += `<div class="error">✗ JavaScript error: ${error.message}</div>`;
            }
        }
        
        // Run tests when page loads
        window.addEventListener('load', function() {
            checkCSS();
            testJS();
        });
        
        // Log any errors
        window.addEventListener('error', function(e) {
            console.error('Asset loading error:', e);
            const results = document.getElementById('js-results');
            results.innerHTML += `<div class="error">✗ Error: ${e.message}</div>`;
        });
    </script>
</body>
</html>
