<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Asset Test</title>
    
    <!-- Test the exact same asset loading as the app -->
    <link rel="stylesheet" href="/build/assets/app-Bf4Uiskv.css">
    <link rel="stylesheet" href="/css/emergency-fix.css">
    
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 8px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        .code { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 4px; 
            font-family: monospace; 
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Comprehensive Asset Loading Test</h1>
    
    <div class="test-section">
        <h2>1. Asset File Verification</h2>
        <div id="file-check"></div>
    </div>
    
    <div class="test-section">
        <h2>2. CSS Loading Test</h2>
        <div id="css-status"></div>
        
        <!-- Test Tailwind classes -->
        <div class="mt-4">
            <h3>Tailwind CSS Test:</h3>
            <div class="bg-blue-500 text-white p-4 rounded-lg shadow-md">
                ✓ If this box is blue with white text and has rounded corners and shadow, Tailwind is working!
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div class="bg-green-100 p-3 rounded">Grid item 1</div>
                <div class="bg-red-100 p-3 rounded">Grid item 2</div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>3. JavaScript Loading Test</h2>
        <div id="js-status"></div>
        
        <!-- Test Alpine.js -->
        <div class="mt-4">
            <h3>Alpine.js Test:</h3>
            <div x-data="{ message: 'Alpine.js is not working', count: 0 }">
                <p x-text="message" class="mb-2"></p>
                <button @click="count++; message = 'Alpine.js is working! Clicked ' + count + ' times'" 
                        class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    Test Alpine.js (Click me!)
                </button>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>4. Network Requests Test</h2>
        <div id="network-status"></div>
        <button onclick="testNetworkRequests()" class="bg-blue-500 text-white px-4 py-2 rounded">
            Test Network Requests
        </button>
    </div>
    
    <div class="test-section">
        <h2>5. Console Logs</h2>
        <div id="console-logs" class="code"></div>
    </div>

    <!-- Load JS assets -->
    <script type="module" src="/build/assets/app-BLkyItOF.js"></script>
    
    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const logs = [];
        
        function captureLog(type, ...args) {
            logs.push(`[${type.toUpperCase()}] ${args.join(' ')}`);
            updateConsoleLogs();
        }
        
        console.log = (...args) => {
            originalLog(...args);
            captureLog('log', ...args);
        };
        
        console.error = (...args) => {
            originalError(...args);
            captureLog('error', ...args);
        };
        
        console.warn = (...args) => {
            originalWarn(...args);
            captureLog('warn', ...args);
        };
        
        function updateConsoleLogs() {
            document.getElementById('console-logs').innerHTML = logs.slice(-10).join('<br>');
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded');
            
            // Test 1: File verification
            testFileVerification();
            
            // Test 2: CSS verification
            testCSSLoading();
            
            // Test 3: JS verification
            setTimeout(testJSLoading, 500);
        });
        
        function testFileVerification() {
            const fileCheck = document.getElementById('file-check');
            
            // Check manifest
            fetch('/build/manifest.json')
                .then(response => response.json())
                .then(manifest => {
                    fileCheck.innerHTML += '<div class="status success">✓ Manifest loaded successfully</div>';
                    fileCheck.innerHTML += `<div class="code">CSS: ${manifest['resources/css/app.css'].file}<br>JS: ${manifest['resources/js/app.js'].file}</div>`;
                    
                    // Test actual file access
                    return Promise.all([
                        fetch(`/build/${manifest['resources/css/app.css'].file}`),
                        fetch(`/build/${manifest['resources/js/app.js'].file}`)
                    ]);
                })
                .then(responses => {
                    const [cssResponse, jsResponse] = responses;
                    if (cssResponse.ok) {
                        fileCheck.innerHTML += '<div class="status success">✓ CSS file accessible</div>';
                    } else {
                        fileCheck.innerHTML += '<div class="status error">✗ CSS file not accessible</div>';
                    }
                    
                    if (jsResponse.ok) {
                        fileCheck.innerHTML += '<div class="status success">✓ JS file accessible</div>';
                    } else {
                        fileCheck.innerHTML += '<div class="status error">✗ JS file not accessible</div>';
                    }
                })
                .catch(error => {
                    fileCheck.innerHTML += '<div class="status error">✗ Error checking files: ' + error.message + '</div>';
                });
        }
        
        function testCSSLoading() {
            const cssStatus = document.getElementById('css-status');
            
            // Check if stylesheets are loaded
            const stylesheets = Array.from(document.styleSheets);
            cssStatus.innerHTML = `<div class="status info">Found ${stylesheets.length} stylesheets</div>`;
            
            stylesheets.forEach((sheet, index) => {
                try {
                    const href = sheet.href || 'inline';
                    const rules = sheet.cssRules ? sheet.cssRules.length : 'unknown';
                    cssStatus.innerHTML += `<div class="status success">✓ Stylesheet ${index + 1}: ${href} (${rules} rules)</div>`;
                } catch (e) {
                    cssStatus.innerHTML += `<div class="status warning">⚠ Stylesheet ${index + 1}: Access restricted</div>`;
                }
            });
            
            // Test Tailwind specifically
            const testEl = document.createElement('div');
            testEl.className = 'bg-red-500 text-white p-4';
            document.body.appendChild(testEl);
            
            const styles = window.getComputedStyle(testEl);
            const bgColor = styles.backgroundColor;
            const color = styles.color;
            const padding = styles.padding;
            
            if (bgColor.includes('239') || bgColor.includes('rgb(239, 68, 68)')) {
                cssStatus.innerHTML += '<div class="status success">✓ Tailwind CSS classes working correctly</div>';
            } else {
                cssStatus.innerHTML += `<div class="status error">✗ Tailwind CSS not working (bg: ${bgColor})</div>`;
            }
            
            document.body.removeChild(testEl);
        }
        
        function testJSLoading() {
            const jsStatus = document.getElementById('js-status');
            
            // Check for Alpine.js
            if (typeof window.Alpine !== 'undefined') {
                jsStatus.innerHTML += '<div class="status success">✓ Alpine.js loaded</div>';
            } else {
                jsStatus.innerHTML += '<div class="status error">✗ Alpine.js not found</div>';
            }
            
            // Check for axios
            if (typeof window.axios !== 'undefined') {
                jsStatus.innerHTML += '<div class="status success">✓ Axios loaded</div>';
            } else {
                jsStatus.innerHTML += '<div class="status warning">⚠ Axios not found</div>';
            }
            
            // Check for other globals
            const globals = ['Alpine', 'axios'];
            globals.forEach(global => {
                if (window[global]) {
                    jsStatus.innerHTML += `<div class="status info">ℹ ${global} available</div>`;
                }
            });
        }
        
        function testNetworkRequests() {
            const networkStatus = document.getElementById('network-status');
            networkStatus.innerHTML = '<div class="status info">Testing network requests...</div>';
            
            // Test axios if available
            if (typeof window.axios !== 'undefined') {
                window.axios.get('/build/manifest.json')
                    .then(response => {
                        networkStatus.innerHTML += '<div class="status success">✓ Axios GET request successful</div>';
                    })
                    .catch(error => {
                        networkStatus.innerHTML += '<div class="status error">✗ Axios GET request failed: ' + error.message + '</div>';
                    });
            } else {
                networkStatus.innerHTML += '<div class="status warning">⚠ Axios not available for testing</div>';
            }
            
            // Test fetch
            fetch('/build/manifest.json')
                .then(response => response.json())
                .then(data => {
                    networkStatus.innerHTML += '<div class="status success">✓ Fetch API request successful</div>';
                })
                .catch(error => {
                    networkStatus.innerHTML += '<div class="status error">✗ Fetch API request failed: ' + error.message + '</div>';
                });
        }
        
        // Error handling
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled promise rejection:', e.reason);
        });
    </script>
</body>
</html>
