import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/js/**/*.js',
        './public/**/*.html',
    ],

    theme: {
        extend: {
            fontFamily: {
                sans: ['Figtree', ...defaultTheme.fontFamily.sans],
            },
            colors: {
                primary: {
                    50: '#f0f9f0',
                    100: '#dcf2dc',
                    200: '#bce5bc',
                    300: '#8dd18d',
                    400: '#57b757',
                    500: '#36a336',
                    600: '#2a8a2a',
                    700: '#236d23',
                    800: '#1f571f',
                    900: '#1c481c',
                    950: '#0a260a',
                },
                secondary: {
                    50: '#fefbf3',
                    100: '#fdf6e3',
                    200: '#faebc2',
                    300: '#f6da97',
                    400: '#f1c464',
                    500: '#F6A840',
                    600: '#e08d1c',
                    700: '#bb7018',
                    800: '#97591a',
                    900: '#7c4a19',
                    950: '#43250b',
                },
            },
            animation: {
                'fade-in': 'fadeIn 0.5s ease-in-out',
                'slide-up': 'slideUp 0.3s ease-out',
                'slide-down': 'slideDown 0.3s ease-out',
                'bounce-slow': 'bounce 2s infinite',
                'ping-slow': 'ping 2s cubic-bezier(0, 0, 0.2, 1) infinite',
            },
            keyframes: {
                fadeIn: {
                    '0%': { opacity: '0' },
                    '100%': { opacity: '1' },
                },
                slideUp: {
                    '0%': { transform: 'translateY(100%)', opacity: '0' },
                    '100%': { transform: 'translateY(0)', opacity: '1' },
                },
                slideDown: {
                    '0%': { transform: 'translateY(-100%)', opacity: '0' },
                    '100%': { transform: 'translateY(0)', opacity: '1' },
                },
            },
            spacing: {
                '18': '4.5rem',
                '88': '22rem',
                '128': '32rem',
            },
            maxWidth: {
                '8xl': '88rem',
                '9xl': '96rem',
            },
            zIndex: {
                '60': '60',
                '70': '70',
                '80': '80',
                '90': '90',
                '100': '100',
            },
        },
    },

    plugins: [
        forms,
        function({ addUtilities }) {
            const newUtilities = {
                '.text-shadow': {
                    textShadow: '2px 2px 4px rgba(0,0,0,0.1)',
                },
                '.text-shadow-md': {
                    textShadow: '4px 4px 8px rgba(0,0,0,0.12), 2px 2px 4px rgba(0,0,0,0.08)',
                },
                '.text-shadow-lg': {
                    textShadow: '15px 15px 30px rgba(0,0,0,0.11), 5px 5px 15px rgba(0,0,0,0.08)',
                },
                '.text-shadow-none': {
                    textShadow: 'none',
                },
                '.backface-hidden': {
                    backfaceVisibility: 'hidden',
                },
                '.transform-gpu': {
                    transform: 'translateZ(0)',
                },
            }
            addUtilities(newUtilities)
        }
    ],
};
