<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class CheckAssets extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assets:check {--fix : Attempt to fix common asset issues}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check the status of Vite assets and diagnose loading issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Checking Vite Assets Status...');
        $this->newLine();

        $issues = [];
        $fixes = [];

        // Check manifest file
        $manifestPath = public_path('build/manifest.json');
        if (!File::exists($manifestPath)) {
            $issues[] = '❌ Manifest file missing: ' . $manifestPath;
            $fixes[] = 'Run: npm run build';
        } else {
            $this->info('✅ Manifest file exists');
            
            try {
                $manifest = json_decode(File::get($manifestPath), true);
                
                if (!$manifest) {
                    $issues[] = '❌ Manifest file is invalid JSON';
                    $fixes[] = 'Run: npm run build';
                } else {
                    $this->info('✅ Manifest file is valid JSON');
                    
                    // Check CSS file
                    $cssFile = $manifest['resources/css/app.css']['file'] ?? null;
                    if ($cssFile) {
                        $cssPath = public_path('build/' . $cssFile);
                        if (File::exists($cssPath)) {
                            $size = File::size($cssPath);
                            $this->info("✅ CSS file exists: {$cssFile} ({$this->formatBytes($size)})");
                        } else {
                            $issues[] = "❌ CSS file missing: {$cssFile}";
                            $fixes[] = 'Run: npm run build';
                        }
                    } else {
                        $issues[] = '❌ CSS file not defined in manifest';
                        $fixes[] = 'Check vite.config.js input configuration';
                    }
                    
                    // Check JS file
                    $jsFile = $manifest['resources/js/app.js']['file'] ?? null;
                    if ($jsFile) {
                        $jsPath = public_path('build/' . $jsFile);
                        if (File::exists($jsPath)) {
                            $size = File::size($jsPath);
                            $this->info("✅ JS file exists: {$jsFile} ({$this->formatBytes($size)})");
                        } else {
                            $issues[] = "❌ JS file missing: {$jsFile}";
                            $fixes[] = 'Run: npm run build';
                        }
                    } else {
                        $issues[] = '❌ JS file not defined in manifest';
                        $fixes[] = 'Check vite.config.js input configuration';
                    }
                }
            } catch (\Exception $e) {
                $issues[] = '❌ Error reading manifest: ' . $e->getMessage();
                $fixes[] = 'Run: npm run build';
            }
        }

        // Check source files
        $this->newLine();
        $this->info('📁 Checking source files...');
        
        $cssSource = resource_path('css/app.css');
        if (File::exists($cssSource)) {
            $this->info('✅ Source CSS file exists');
        } else {
            $issues[] = '❌ Source CSS file missing: ' . $cssSource;
            $fixes[] = 'Create resources/css/app.css with Tailwind directives';
        }
        
        $jsSource = resource_path('js/app.js');
        if (File::exists($jsSource)) {
            $this->info('✅ Source JS file exists');
        } else {
            $issues[] = '❌ Source JS file missing: ' . $jsSource;
            $fixes[] = 'Create resources/js/app.js';
        }

        // Check Vite config
        $viteConfig = base_path('vite.config.js');
        if (File::exists($viteConfig)) {
            $this->info('✅ Vite config exists');
        } else {
            $issues[] = '❌ Vite config missing: ' . $viteConfig;
            $fixes[] = 'Create vite.config.js with Laravel plugin configuration';
        }

        // Check package.json
        $packageJson = base_path('package.json');
        if (File::exists($packageJson)) {
            $this->info('✅ Package.json exists');
            
            try {
                $package = json_decode(File::get($packageJson), true);
                if (isset($package['scripts']['build'])) {
                    $this->info('✅ Build script defined');
                } else {
                    $issues[] = '❌ Build script not defined in package.json';
                    $fixes[] = 'Add "build": "vite build" to package.json scripts';
                }
            } catch (\Exception $e) {
                $issues[] = '❌ Error reading package.json: ' . $e->getMessage();
            }
        } else {
            $issues[] = '❌ Package.json missing';
            $fixes[] = 'Run: npm init';
        }

        // Check node_modules
        $nodeModules = base_path('node_modules');
        if (File::exists($nodeModules)) {
            $this->info('✅ Node modules installed');
        } else {
            $issues[] = '❌ Node modules not installed';
            $fixes[] = 'Run: npm install';
        }

        // Check emergency CSS
        $emergencyCSS = public_path('css/emergency-fix.css');
        if (File::exists($emergencyCSS)) {
            $this->info('✅ Emergency CSS exists');
        } else {
            $issues[] = '❌ Emergency CSS missing: ' . $emergencyCSS;
            $fixes[] = 'Create public/css/emergency-fix.css with basic styles';
        }

        // Summary
        $this->newLine();
        if (empty($issues)) {
            $this->info('🎉 All assets are properly configured!');
        } else {
            $this->error('⚠️  Found ' . count($issues) . ' issue(s):');
            foreach ($issues as $issue) {
                $this->line($issue);
            }
            
            $this->newLine();
            $this->info('💡 Suggested fixes:');
            foreach (array_unique($fixes) as $fix) {
                $this->line('   ' . $fix);
            }
        }

        // Auto-fix option
        if ($this->option('fix') && !empty($issues)) {
            $this->newLine();
            $this->info('🔧 Attempting to fix issues...');
            
            if (in_array('Run: npm run build', $fixes)) {
                $this->info('Running npm run build...');
                $result = shell_exec('cd ' . base_path() . ' && npm run build 2>&1');
                $this->line($result);
            }
            
            if (in_array('Run: npm install', $fixes)) {
                $this->info('Running npm install...');
                $result = shell_exec('cd ' . base_path() . ' && npm install 2>&1');
                $this->line($result);
            }
            
            $this->info('✅ Auto-fix completed. Run the command again to verify.');
        }

        return empty($issues) ? 0 : 1;
    }

    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
