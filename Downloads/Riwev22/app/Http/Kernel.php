<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON><PERSON> extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array<int, class-string|string>
     */
    protected $middleware = [
        // \App\Http\Middleware\TrustHosts::class,
        \App\Http\Middleware\TrustProxies::class,
        \Illuminate\Http\Middleware\HandleCors::class,
        \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        \App\Http\Middleware\SecurityHeaders::class,
        \App\Http\Middleware\RedirectApiRequests::class,
        \App\Http\Middleware\CheckMaintenanceMode::class,
        \App\Http\Middleware\HandlePostOnlyRoutes::class,
        \App\Http\Middleware\IncreaseUploadLimits::class,
        \App\Http\Middleware\CheckInstallation::class,
        \App\Http\Middleware\SecurityMonitor::class,
        \App\Http\Middleware\BlockedAccessCheck::class,
        \App\Http\Middleware\GeoBlockingMiddleware::class,
        \App\Http\Middleware\AssetHeaders::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array<string, array<int, class-string|string>>
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\SafariCsrfFix::class, // Add Safari fix before CSRF verification
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\InputSanitization::class, // Sanitize all input
            \App\Http\Middleware\TrafficTracking::class, // Track all website visitors
            \App\Http\Middleware\SEOMiddleware::class, // SEO optimization
            \App\Http\Middleware\EnsureBankAccess::class,
            \App\Http\Middleware\EnsureInsurerAccess::class,
        ],

        'api' => [
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            \Illuminate\Routing\Middleware\ThrottleRequests::class.':api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],
    ];

    /**
     * The application's middleware aliases.
     *
     * Aliases may be used instead of class names to conveniently assign middleware to routes and groups.
     *
     * @var array<string, class-string|string>
     */
    protected $middlewareAliases = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'auth.session' => \Illuminate\Session\Middleware\AuthenticateSession::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'precognitive' => \Illuminate\Foundation\Http\Middleware\HandlePrecognitiveRequests::class,
        'signed' => \App\Http\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        'role' => \App\Http\Middleware\CheckRole::class,
        'permission' => \App\Http\Middleware\CheckPermission::class,
        'subscription' => \App\Http\Middleware\CheckSubscription::class,
        'admin' => \App\Http\Middleware\AdminMiddleware::class,
        'admin.2fa' => \App\Http\Middleware\AdminTwoFactorAuth::class,
        'verify.2fa.method' => \App\Http\Middleware\VerifyTwoFactorMethod::class,
        // 'twofactor' => \App\Http\Middleware\TwoFactorAuth::class, // Using class directly in routes
        'registration.enabled' => \App\Http\Middleware\CheckRegistrationEnabled::class,
        'kyc.verified' => \App\Http\Middleware\RequireKYC::class,
        'developer' => \App\Http\Middleware\DeveloperMiddleware::class,
        'api.key' => \App\Http\Middleware\ApiKeyMiddleware::class,
        'api.auth' => \App\Http\Middleware\ApiAuthenticate::class,
        'role.redirect' => \App\Http\Middleware\RoleBasedRedirect::class,
        'agent' => \App\Http\Middleware\AgentMiddleware::class,
        'bank' => \App\Http\Middleware\BankMiddleware::class,
        'check.rcp.access' => \App\Http\Middleware\CheckRCPScoreAccess::class,
        'check.loan.eligibility' => \App\Http\Middleware\CheckLoanEligibility::class,
        'check.insurance.eligibility' => \App\Http\Middleware\CheckInsuranceEligibility::class,
        'secure.upload' => \App\Http\Middleware\SecureFileUpload::class,
        'input.sanitize' => \App\Http\Middleware\InputSanitization::class,
        'check.location' => \App\Http\Middleware\CheckLocationRestriction::class,
        'ensure.bank.access' => \App\Http\Middleware\EnsureBankAccess::class,
        'business.module' => \App\Http\Middleware\BusinessModuleMiddleware::class,
        'prevent.duplicates' => \App\Http\Middleware\PreventDuplicateSubmissions::class,
        'security.monitor' => \App\Http\Middleware\SecurityMonitor::class,
        'admin.access.control' => \App\Http\Middleware\AdminAccessControl::class,
        'blocked.access.check' => \App\Http\Middleware\BlockedAccessCheck::class,
        'geo.blocking' => \App\Http\Middleware\GeoBlockingMiddleware::class,
        'bank.security' => \App\Http\Middleware\BankSecurityMiddleware::class,
        'asset.headers' => \App\Http\Middleware\AssetHeaders::class,
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array<string, class-string|string>
     */
    protected $routeMiddleware = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'auth.session' => \Illuminate\Session\Middleware\AuthenticateSession::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'precognitive' => \Illuminate\Foundation\Http\Middleware\HandlePrecognitiveRequests::class,
        'signed' => \App\Http\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        'role' => \App\Http\Middleware\CheckRole::class,
        'permission' => \App\Http\Middleware\CheckPermission::class,
        'subscription' => \App\Http\Middleware\CheckSubscription::class,
        'admin' => \App\Http\Middleware\AdminMiddleware::class,
        'admin.2fa' => \App\Http\Middleware\AdminTwoFactorAuth::class,
        'verify.2fa.method' => \App\Http\Middleware\VerifyTwoFactorMethod::class,
        // 'twofactor' => \App\Http\Middleware\TwoFactorAuth::class, // Using class directly in routes
        'registration.enabled' => \App\Http\Middleware\CheckRegistrationEnabled::class,
        'kyc.verified' => \App\Http\Middleware\RequireKYC::class,
        'developer' => \App\Http\Middleware\DeveloperMiddleware::class,
        'api.key' => \App\Http\Middleware\ApiKeyMiddleware::class,
        'api.auth' => \App\Http\Middleware\ApiAuthenticate::class,
        'role.redirect' => \App\Http\Middleware\RoleBasedRedirect::class,
        'agent' => \App\Http\Middleware\AgentMiddleware::class,
        'bank' => \App\Http\Middleware\BankMiddleware::class,
        'insurer' => \App\Http\Middleware\InsurerMiddleware::class,
        'check.rcp.access' => \App\Http\Middleware\CheckRCPScoreAccess::class,
        'check.loan.eligibility' => \App\Http\Middleware\CheckLoanEligibility::class,
        'check.insurance.eligibility' => \App\Http\Middleware\CheckInsuranceEligibility::class,
        'check.location' => \App\Http\Middleware\CheckLocationRestriction::class,
        'ensure.bank.access' => \App\Http\Middleware\EnsureBankAccess::class,
        'ensure.insurer.access' => \App\Http\Middleware\EnsureInsurerAccess::class,
        'block.non.user' => \App\Http\Middleware\BlockNonUserAccess::class,
        'business.module' => \App\Http\Middleware\BusinessModuleMiddleware::class,
        'prevent.duplicates' => \App\Http\Middleware\PreventDuplicateSubmissions::class,
        'admin.access.control' => \App\Http\Middleware\AdminAccessControl::class,
        'blocked.access.check' => \App\Http\Middleware\BlockedAccessCheck::class,
        'geo.blocking' => \App\Http\Middleware\GeoBlockingMiddleware::class,
        'secure.upload' => \App\Http\Middleware\SecureFileUpload::class,
        'input.sanitize' => \App\Http\Middleware\InputSanitization::class,
        'security.monitor' => \App\Http\Middleware\SecurityMonitor::class,
        'block.developer' => \App\Http\Middleware\BlockDeveloperAccess::class,
    ];
}