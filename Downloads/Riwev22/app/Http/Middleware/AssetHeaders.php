<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AssetHeaders
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only apply to asset requests
        if ($this->isAssetRequest($request)) {
            // Set proper caching headers for assets
            $response->headers->set('Cache-Control', 'public, max-age=31536000, immutable');
            
            // Set proper MIME types
            $path = $request->path();
            if (str_ends_with($path, '.css')) {
                $response->headers->set('Content-Type', 'text/css');
            } elseif (str_ends_with($path, '.js')) {
                $response->headers->set('Content-Type', 'application/javascript');
            }
            
            // Add security headers for assets
            $response->headers->set('X-Content-Type-Options', 'nosniff');
        }

        return $response;
    }

    /**
     * Determine if the request is for an asset file.
     */
    private function isAssetRequest(Request $request): bool
    {
        $path = $request->path();
        
        return str_starts_with($path, 'build/') || 
               str_starts_with($path, 'css/') || 
               str_starts_with($path, 'js/') ||
               str_starts_with($path, 'assets/');
    }
}
