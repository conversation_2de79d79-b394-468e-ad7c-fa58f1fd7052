<?php

use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\PasswordController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\TwoFactorAuthController;
use App\Http\Controllers\AccountController;
use App\Http\Controllers\ApiProxyController;
use App\Http\Controllers\BankAccountController;
use App\Http\Controllers\ClaimController;
use App\Http\Controllers\CropInsightsController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DigitalCardController;
use App\Http\Controllers\MarketAnalysisController;
use App\Http\Controllers\FarmController;
use App\Http\Controllers\InstallController;
use App\Http\Controllers\KYCController;
use App\Http\Controllers\NoticeController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\PaystackController;
use App\Http\Controllers\PolicyController;
use App\Http\Controllers\RiskAnalysisController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\BusinessModuleSubscriptionController;
use App\Http\Controllers\SupportController;
use App\Http\Controllers\WalletController;
use App\Http\Controllers\WeatherController;
use App\Http\Controllers\SoilSensorAlertController;
use App\Http\Controllers\SoilSensorController;
use App\Http\Controllers\Admin\CropController;
use App\Http\Controllers\WhatsAppWebhookController;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

// CSRF Token route - used by AJAX requests only
Route::get('/csrf-token', function(Request $request) {
    // Only allow AJAX requests to this route
    if (!$request->ajax() && !$request->wantsJson()) {
        abort(404);
    }

    return response()->json(['csrf_token' => csrf_token()]);
})->name('csrf.token');

// Installation Routes
Route::prefix('install')->group(function () {
    Route::get('/', [InstallController::class, 'welcome'])->name('install.welcome');
    Route::get('/database', [InstallController::class, 'database'])->name('install.database');
    Route::post('/database', [InstallController::class, 'storeDatabaseConfig'])->name('install.database.store');
    Route::get('/environment', [InstallController::class, 'environment'])->name('install.environment');
    Route::post('/environment', [InstallController::class, 'storeEnvironmentConfig'])->name('install.environment.store');
    Route::get('/admin', [InstallController::class, 'admin'])->name('install.admin');
    Route::post('/admin', [InstallController::class, 'storeAdminUser'])->name('install.admin.store');
    Route::get('/complete', [InstallController::class, 'complete'])->name('install.complete');
});

// Blocked page routes (must be before blocked access middleware)
Route::get('/blocked', [\App\Http\Controllers\BlockedController::class, 'index'])->name('blocked');
Route::post('/blocked/contact', [\App\Http\Controllers\BlockedController::class, 'contact'])->name('blocked.contact');

// Fix for verify.2fa.method class not found
Route::middleware('web')->group(function () {
    Route::get('/verify-2fa', function() {
        return redirect()->route('dashboard');
    })->name('verify.2fa.method');
});

// Test routes
Route::middleware(['auth'])->group(function () {
    Route::get('/test/paystack', function() {
        return view('tests.paystack');
    })->name('test.paystack');

    Route::get('/test/paystack-webhook', function() {
        return view('tests.paystack-webhook');
    })->name('test.paystack-webhook');

    Route::get('/test/paystack-webhook-simulator', function() {
        return view('tests.paystack-webhook-simulator');
    })->name('test.paystack-webhook-simulator');

    Route::get('/test/paystack-logs', function() {
        return view('tests.paystack-logs');
    })->name('test.paystack-logs');

    // Temporary endpoint to get Paystack API logs
    Route::get('/paystack/logs', function() {
        // Get the latest logs from the log file
        $logPath = storage_path('logs/laravel.log');

        if (!file_exists($logPath)) {
            return response()->json([
                'error' => 'Log file not found',
                'path' => $logPath
            ], 404);
        }

        // Read the log file
        $logContent = file_get_contents($logPath);

        // Extract Paystack API logs
        $paystackLogs = [];
        $requestLogs = [];
        $responseLogs = [];

        // Parse the log file
        $pattern = '/\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\].*?Paystack DVA API.*?(?=\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\]|$)/s';
        if (preg_match_all($pattern, $logContent, $matches)) {
            foreach ($matches[0] as $match) {
                $paystackLogs[] = $match;

                if (strpos($match, 'Paystack DVA API Request Payload') !== false) {
                    $requestLogs[] = $match;
                }

                if (strpos($match, 'Paystack DVA API Response') !== false) {
                    $responseLogs[] = $match;
                }
            }
        }

        // Limit the number of logs
        $paystackLogs = array_slice($paystackLogs, 0, 10);
        $requestLogs = array_slice($requestLogs, 0, 5);
        $responseLogs = array_slice($responseLogs, 0, 5);

        return response()->json([
            'all_logs' => $paystackLogs,
            'request_logs' => $requestLogs,
            'response_logs' => $responseLogs
        ]);
    })->name('paystack.logs');

    // Test endpoint to check dedicated virtual accounts
    Route::get('/paystack/check-dva/{virtualAccountId?}', function($virtualAccountId = null) {
        try {
            // Get the virtual account
            $virtualAccount = $virtualAccountId ? \App\Models\VirtualAccount::find($virtualAccountId) : \App\Models\VirtualAccount::where('status', 'pending')->first();

            if (!$virtualAccount) {
                return response()->json([
                    'success' => false,
                    'message' => 'No pending virtual account found'
                ]);
            }

            // Get the customer code
            $customerCode = $virtualAccount->metadata['customer_code'] ?? null;

            if (!$customerCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'No customer code found in virtual account metadata'
                ]);
            }

            // Call the Paystack API
            $response = \Illuminate\Support\Facades\Http::withHeaders([
                'Authorization' => 'Bearer ' . config('payment.paystack.secret_key'),
                'Content-Type' => 'application/json'
            ])->get('https://api.paystack.co/dedicated_account', [
                'customer' => $customerCode
            ]);

            // Log the response
            \Illuminate\Support\Facades\Log::info('Paystack List DVA API Test Response', [
                'virtual_account_id' => $virtualAccount->id,
                'customer_code' => $customerCode,
                'status_code' => $response->status(),
                'response_body' => $response->json()
            ]);

            // Check if the account is available
            $data = $response->json()['data'] ?? [];
            $accountFound = !empty($data);

            // If account is found and the virtual account is pending, update it
            if ($accountFound && $virtualAccount->status === 'pending' && $virtualAccount->account_number === 'pending') {
                $matchingAccount = null;

                // Find the account that matches the preferred bank
                foreach ($data as $account) {
                    if ($account['bank']['slug'] === $virtualAccount->bank_slug) {
                        $matchingAccount = $account;
                        break;
                    }
                }

                // If no matching account is found, use the first one
                if (!$matchingAccount && !empty($data)) {
                    $matchingAccount = $data[0];
                }

                if ($matchingAccount) {
                    // Create verification data
                    $verificationData = [
                        'verified_at' => now()->toIso8601String(),
                        'account_name' => $matchingAccount['account_name'],
                        'account_number' => $matchingAccount['account_number'],
                        'bank_code' => $matchingAccount['bank']['id'],
                        'bank_name' => $matchingAccount['bank']['name'],
                    ];

                    // Update the virtual account with the real account details
                    $virtualAccount->update([
                        'account_name' => $matchingAccount['account_name'],
                        'account_number' => $matchingAccount['account_number'],
                        'bank_name' => $matchingAccount['bank']['name'],
                        'bank_code' => $matchingAccount['bank']['id'],
                        'bank_slug' => $matchingAccount['bank']['slug'],
                        'currency' => $matchingAccount['currency'],
                        'active' => $matchingAccount['active'],
                        'verified' => true,
                        'verified_at' => now(),
                        'verification_data' => $verificationData,
                        'provider_reference' => $matchingAccount['id'] ?? null,
                        'status' => 'active',
                        'metadata' => array_merge($virtualAccount->metadata ?? [], [
                            'dedicated_account_id' => $matchingAccount['id'] ?? null,
                            'assignment' => $matchingAccount['assignment'] ?? null,
                            'updated_at' => now()->toIso8601String()
                        ])
                    ]);

                    \Illuminate\Support\Facades\Log::info('Virtual account updated with real account details', [
                        'virtual_account_id' => $virtualAccount->id,
                        'account_number' => $matchingAccount['account_number'],
                        'bank' => $matchingAccount['bank']['name']
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'virtual_account' => $virtualAccount->fresh(),
                'customer_code' => $customerCode,
                'account_found' => $accountFound,
                'response' => $response->json()
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error checking dedicated virtual account', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    })->name('paystack.check-dva');
});

// Public test routes for Paystack (no auth required)
Route::get('/test/paystack-minimal', function() {
    return view('tests.paystack-minimal');
})->name('test.paystack.minimal');

Route::get('/test/paystack-inline', function() {
    return view('wallet.inline-paystack');
})->name('test.paystack.inline');

// Legal pages
Route::get('/privacy-policy', function() {
    return view('privacy-policy');
})->name('privacy.policy');

Route::get('/terms-of-service', function() {
    return view('terms-of-service');
})->name('terms.service');

// Event Documentation Pages
Route::get('/admin/events/documentation', function() {
    return view('admin.events.documentation');
})->name('admin.events.documentation');

Route::get('/admin/events/mailchimp-docs', function() {
    return view('admin.events.mailchimp-docs');
})->name('admin.events.mailchimp-docs');

// Event Export Route (without auth for testing)
Route::get('/admin/events/{event}/registrations/export', [\App\Http\Controllers\Admin\EventRegistrationController::class, 'export'])
    ->name('admin.events.registrations.export');

// Pay4Me routes - publicly accessible
Route::get('/pay4me', [\App\Http\Controllers\Pay4MeController::class, 'index'])->name('pay4me.index');
Route::get('/pay4me/card', [\App\Http\Controllers\Pay4MeController::class, 'card'])->name('pay4me.card');
Route::post('/pay4me/fetch-policy', [\App\Http\Controllers\Pay4MeController::class, 'fetchPolicy'])->name('pay4me.fetch-policy');
Route::post('/pay4me/pay-policy', [\App\Http\Controllers\Pay4MeController::class, 'payPolicy'])->name('pay4me.pay-policy');
Route::post('/pay4me/donate', [\App\Http\Controllers\Pay4MeController::class, 'donate'])->name('pay4me.donate');

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

// Weather routes
Route::middleware(['auth', 'subscription', 'block.developer'])->group(function () {
    Route::get('/weather', [WeatherController::class, 'index'])->name('weather.index');
    Route::get('/weather/farm/{farm}', [WeatherController::class, 'getFarmWeather'])->name('weather.farm');

    // Weather export and email
    Route::post('/weather/export-pdf', [WeatherController::class, 'exportPdf'])->name('weather.export-pdf');
    Route::post('/weather/email-report', [WeatherController::class, 'emailReport'])->name('weather.email-report');

    // Weather Alerts
    Route::get('/weather-alerts', [\App\Http\Controllers\WeatherAlertController::class, 'index'])->name('weather-alerts.index');
    Route::get('/weather-alerts/{alert}', [\App\Http\Controllers\WeatherAlertController::class, 'show'])->name('weather-alerts.show');
    Route::post('/weather-alerts/{alert}/resolve', [\App\Http\Controllers\WeatherAlertController::class, 'resolve'])->name('weather-alerts.resolve');

    // Fertility Visualization (Coming Soon)
    Route::get('/fertility-visualization', [\App\Http\Controllers\FertilityVisualizationController::class, 'index'])->name('fertility-visualization.index')->middleware('coming.soon');
    Route::get('/api/farms/{farm}/boundaries', [\App\Http\Controllers\FertilityVisualizationController::class, 'getBoundaries'])->name('api.farms.boundaries');

    // Simple Fertility Visualization (for debugging)
    Route::get('/fertility-visualization-simple', [\App\Http\Controllers\FertilityVisualizationController::class, 'indexSimple'])->name('fertility-visualization-simple');

    // Test Map (for debugging Google Maps)
    Route::get('/test-map', function() {
        return view('farms.test-map');
    })->name('test-map');

    // Test Google Maps (for debugging Google Maps API)
    Route::get('/test-google-maps', function() {
        return view('farms.test-google-maps');
    })->name('test-google-maps');

    // Direct Map Test (for debugging Google Maps API with a simpler approach)
    Route::get('/direct-map', function() {
        return view('farms.direct-map');
    })->name('direct-map');

    // Test Farm Data (for debugging fertility visualization)
    Route::get('/test-farm-data/{id}', function($id) {
        $farm = \App\Models\Farm::findOrFail($id);
        $controller = new \App\Http\Controllers\FertilityVisualizationController();
        $boundaries = $controller->getFarmBoundaries($farm);

        // Get crop insights data
        $cropInsightsController = new \App\Http\Controllers\CropInsightsController();
        $insights = $cropInsightsController->getFarmInsights($id);

        return response()->json([
            'farm' => $farm,
            'boundaries' => $boundaries,
            'insights' => $insights,
            'fertility' => $insights->fertility ?? null,
            'soil_data' => $insights->soil_data ?? null,
        ]);
    })->name('test-farm-data');
});

// Service Locator routes
Route::middleware(['auth', 'block.developer'])->group(function () {
    // Service locator main pages
    Route::get('/service-locator', [\App\Http\Controllers\ServiceLocatorController::class, 'index'])->name('service-locator.index');
    Route::get('/service-locator/farm/{farm}', [\App\Http\Controllers\ServiceLocatorController::class, 'getFarmProviders'])->name('service-locator.farm');
    Route::post('/service-locator/update-coordinates', [\App\Http\Controllers\ServiceLocatorController::class, 'updateCoordinates'])->name('service-locator.update-coordinates');

    // Booking management
    Route::post('/service-locator/booking', [\App\Http\Controllers\ServiceLocatorController::class, 'storeBooking'])->name('service-locator.booking.store');
    Route::get('/service-locator/bookings', [\App\Http\Controllers\ServiceLocatorController::class, 'bookings'])->name('service-locator.bookings');
    Route::get('/service-locator/booking/{booking}', [\App\Http\Controllers\ServiceLocatorController::class, 'showBooking'])->name('service-locator.booking.show');
    Route::post('/service-locator/booking/{booking}/rate', [\App\Http\Controllers\ServiceLocatorController::class, 'storeRating'])->name('service-locator.booking.rate');
    Route::post('/service-locator/booking/{booking}/cancel', [\App\Http\Controllers\ServiceLocatorController::class, 'cancelBooking'])->name('service-locator.booking.cancel');
});

// SEO Routes
Route::get('/sitemap.xml', [\App\Http\Controllers\SitemapController::class, 'index'])->name('sitemap.index');
Route::get('/sitemap-pages.xml', [\App\Http\Controllers\SitemapController::class, 'pages'])->name('sitemap.pages');
Route::get('/robots.txt', [\App\Http\Controllers\SitemapController::class, 'robots'])->name('robots');

// SEO Test Route
Route::get('/seo-test', function() {
    $seoService = app(\App\Services\SEOService::class);

    return response()->json([
        'meta_tags' => $seoService->generateMetaTags(),
        'breadcrumbs' => $seoService->generateBreadcrumbs(),
        'canonical_url' => $seoService->getCanonicalUrl(),
        'twitter_tags' => $seoService->generateTwitterCardTags(),
        'structured_data' => [
            'organization' => $seoService->generateStructuredData('Organization'),
            'website' => $seoService->generateStructuredData('WebSite'),
        ],
        'sitemap_urls_count' => count($seoService->getSitemapUrls()),
    ], 200, [], JSON_PRETTY_PRINT);
})->name('seo.test');

// Public routes
Route::get('/', [\App\Http\Controllers\HomeController::class, 'index'])->name('home');
Route::get('/our-story', [\App\Http\Controllers\HomeController::class, 'ourStory'])->name('our-story');
Route::get('/use-cases', [\App\Http\Controllers\HomeController::class, 'useCases'])->name('use-cases');
Route::get('/solutions', [\App\Http\Controllers\HomeController::class, 'solutions'])->name('solutions');
Route::get('/ije-ai', [\App\Http\Controllers\HomeController::class, 'ijeAi'])->name('ije-ai');

// Pitch Deck Routes
Route::get('/pitch-deck', function () {
    return view('pitch-deck');
})->name('pitch-deck');

Route::get('/pitch-deck-africa', function () {
    return view('pitch-deck-africa');
})->name('pitch-deck-africa');
Route::get('/rupa', [\App\Http\Controllers\RupaLandingController::class, 'index'])->name('rupa.landing');
Route::get('/rupa-talk-to-us', [\App\Http\Controllers\RupaLandingController::class, 'talkToUs'])->name('rupa.talk-to-us');
Route::post('/rupa-talk-to-us', [\App\Http\Controllers\RupaLandingController::class, 'submitTalkToUs'])->name('rupa.talk-to-us.submit');
Route::get('/rupa-sample/{id}', [\App\Http\Controllers\RupaLandingController::class, 'showProfile'])->name('rupa.profile');



// Chunked upload routes with security
Route::middleware(['auth', 'secure.upload'])->group(function () {
    Route::post('/upload/chunk', [\App\Http\Controllers\ChunkedUploadController::class, 'uploadChunk'])->name('upload.chunk');
    Route::post('/upload/complete', [\App\Http\Controllers\ChunkedUploadController::class, 'completeUpload'])->name('upload.complete');
});

// Test route
Route::get('/test', function() {
    return view('test');
});

// Asset loading test route
Route::get('/test-assets', function() {
    return view('test-assets');
})->name('test.assets');

// Test web search functionality
Route::get('/test-web-search', function () {
    try {
        $webSearchService = app(\App\Services\WebSearchService::class);

        $results = [
            'configuration_test' => $webSearchService->testConfiguration(),
            'market_query_tests' => [],
            'commodity_extraction_tests' => []
        ];

        // Test market price query detection
        $testQueries = [
            "What is the current price of rice in Nigeria?" => true,
            "Latest maize market price today" => true,
            "How much does tomato cost now?" => true,
            "Tell me about farming techniques" => false,
        ];

        foreach ($testQueries as $query => $expected) {
            $isMarketQuery = $webSearchService->isMarketPriceQuery($query);
            $commodity = $webSearchService->extractCommodity($query);

            $results['market_query_tests'][] = [
                'query' => $query,
                'expected' => $expected,
                'detected' => $isMarketQuery,
                'passed' => $isMarketQuery === $expected,
                'commodity' => $commodity
            ];
        }

        // Test commodity extraction
        $commodityTests = [
            "What is the price of rice?" => "rice",
            "How much does maize cost?" => "maize",
            "Tomato market prices" => "tomato",
            "Tell me about farming" => null,
        ];

        foreach ($commodityTests as $query => $expected) {
            $extracted = $webSearchService->extractCommodity($query);

            $results['commodity_extraction_tests'][] = [
                'query' => $query,
                'expected' => $expected,
                'extracted' => $extracted,
                'passed' => $extracted === $expected
            ];
        }

        return response()->json($results, 200, [], JSON_PRETTY_PRINT);

    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

// Test chatbot integration with web search
Route::middleware(['auth'])->get('/test-chatbot-web-search', function () {
    try {
        $user = Auth::user();

        // Test message that should trigger web search
        $testMessage = "What is the current price of rice in Nigeria?";

        $openAIService = app(\App\Services\OpenAIService::class);
        $webSearchService = app(\App\Services\WebSearchService::class);

        // Check if web search would be triggered
        $wouldTriggerWebSearch = $webSearchService->isMarketPriceQuery($testMessage);
        $extractedCommodity = $webSearchService->extractCommodity($testMessage);

        // Mock context
        $context = [
            'user_context' => "User: {$user->name}, Location: Nigeria",
            'farm_data' => []
        ];

        // Send message to OpenAI service
        $response = $openAIService->sendMessage($testMessage, [], $context);

        return response()->json([
            'test_message' => $testMessage,
            'user' => $user->name,
            'web_search_detection' => [
                'would_trigger' => $wouldTriggerWebSearch,
                'extracted_commodity' => $extractedCommodity,
            ],
            'openai_response' => [
                'success' => $response['success'],
                'message_length' => isset($response['message']) ? strlen($response['message']) : 0,
                'web_search_used' => $response['web_search_used'] ?? false,
                'web_search_commodity' => $response['web_search_commodity'] ?? null,
                'web_search_sources_count' => count($response['web_search_sources'] ?? []),
                'confidence' => $response['confidence'] ?? null,
                'intent' => $response['intent'] ?? null,
                'error' => $response['error'] ?? null,
            ],
            'response_preview' => isset($response['message']) ? substr($response['message'], 0, 300) . '...' : null,
        ], 200, [], JSON_PRETTY_PRINT);

    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

// Admin route for web search configuration
Route::middleware(['auth', 'role:admin'])->group(function () {
    Route::get('/admin/web-search-config', function () {
        $config = [
            'web_search_enabled' => App\Models\ChatbotConfiguration::getValue('web_search_enabled', false),
            'google_search_api_key' => !empty(App\Models\ChatbotConfiguration::getValue('google_search_api_key', '')) ? 'Configured' : 'Not configured',
            'google_search_engine_id' => App\Models\ChatbotConfiguration::getValue('google_search_engine_id', ''),
            'web_search_cache_time' => App\Models\ChatbotConfiguration::getValue('web_search_cache_time', 1800),
        ];

        // Test configuration
        $webSearchService = app(\App\Services\WebSearchService::class);
        $testResult = $webSearchService->testConfiguration();

        return response()->json([
            'configuration' => $config,
            'test_result' => $testResult,
            'instructions' => [
                'To configure web search:',
                '1. Set google_search_api_key in ChatbotConfiguration',
                '2. Set google_search_engine_id in ChatbotConfiguration',
                '3. Enable web_search_enabled',
                '4. Test at /test-web-search'
            ]
        ], 200, [], JSON_PRETTY_PRINT);
    });

    Route::post('/admin/web-search-config', function (Request $request) {
        try {
            $validated = $request->validate([
                'web_search_enabled' => 'boolean',
                'google_search_api_key' => 'nullable|string',
                'google_search_engine_id' => 'nullable|string',
                'web_search_cache_time' => 'integer|min:300|max:86400', // 5 minutes to 24 hours
            ]);

            foreach ($validated as $key => $value) {
                if ($value !== null) {
                    $type = $key === 'google_search_api_key' ? 'encrypted' :
                           ($key === 'web_search_enabled' ? 'boolean' :
                           ($key === 'web_search_cache_time' ? 'integer' : 'string'));

                    App\Models\ChatbotConfiguration::setValue($key, $value, $type, 'web_search');
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Web search configuration updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 400);
        }
    });
});

// Test agent chatbot context
Route::middleware(['auth'])->get('/test-agent-context', function() {
    $user = Auth::user();

    if (!$user->isAgent()) {
        return response()->json([
            'error' => 'This test is only for agent users',
            'user_type' => $user->hasRole('agent') ? 'agent' : 'user'
        ]);
    }

    $context = \App\Models\ChatbotUserContext::getOrCreateForUser($user);
    $summary = $context->getContextSummary();

    return response()->json([
        'agent_name' => $user->name,
        'context_length' => strlen($summary),
        'has_agent_profile' => strpos($summary, 'AGENT PROFILE') !== false,
        'has_portfolio_overview' => strpos($summary, 'PORTFOLIO OVERVIEW') !== false,
        'managed_users_count' => $context->farm_data['summary_statistics']['total_managed_users'] ?? 0,
        'managed_farms_count' => $context->farm_data['summary_statistics']['total_managed_farms'] ?? 0,
        'context_preview' => substr($summary, 0, 500) . '...',
    ]);
});

// Test enhanced user chatbot context
Route::middleware(['auth'])->get('/test-user-context', function() {
    $user = Auth::user();

    $context = \App\Models\ChatbotUserContext::getOrCreateForUser($user);
    $summary = $context->getContextSummary();

    return response()->json([
        'user_name' => $user->name,
        'user_type' => $user->isAgent() ? 'agent' : 'user',
        'context_length' => strlen($summary),
        'has_farm_portfolio' => strpos($summary, 'FARM PORTFOLIO') !== false,
        'has_insurance_overview' => strpos($summary, 'INSURANCE OVERVIEW') !== false,
        'has_loan_status' => strpos($summary, 'LOAN STATUS') !== false,
        'has_business_module' => strpos($summary, 'BUSINESS MODULE') !== false,
        'farms_count' => $context->farm_data['farms'] ? count($context->farm_data['farms']) : 0,
        'insurance_policies' => $context->farm_data['insurance_summary']['total_policies'] ?? 0,
        'context_preview' => substr($summary, 0, 800) . '...',
    ]);
});

// Enable chatbot
Route::get('/enable-chatbot', function() {
    try {
        \App\Models\ChatbotConfiguration::setValue('chatbot_enabled', true, 'boolean', 'general');

        $enabled = \App\Models\ChatbotConfiguration::isChatbotEnabled();
        $provider = \App\Models\ChatbotConfiguration::getApiProvider();

        return response()->json([
            'success' => true,
            'message' => 'Chatbot enabled successfully!',
            'enabled' => $enabled,
            'api_provider' => $provider,
        ]);
    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
        ]);
    }
});

// Disable chatbot (for testing)
Route::get('/disable-chatbot', function() {
    try {
        \App\Models\ChatbotConfiguration::setValue('chatbot_enabled', false, 'boolean', 'general');

        $enabled = \App\Models\ChatbotConfiguration::isChatbotEnabled();

        return response()->json([
            'success' => true,
            'message' => 'Chatbot disabled successfully!',
            'enabled' => $enabled,
        ]);
    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
        ]);
    }
});

// Test chatbot performance
Route::middleware(['auth'])->get('/test-chatbot-performance', function() {
    $user = Auth::user();
    $startTime = microtime(true);

    try {
        // Test context loading
        $contextStart = microtime(true);
        $context = \App\Models\ChatbotUserContext::getOrCreateForUser($user);
        $contextTime = (microtime(true) - $contextStart) * 1000;

        // Test context summary generation
        $summaryStart = microtime(true);
        $summary = $context->getContextSummary();
        $summaryTime = (microtime(true) - $summaryStart) * 1000;

        $totalTime = (microtime(true) - $startTime) * 1000;

        return response()->json([
            'success' => true,
            'performance_metrics' => [
                'context_loading_ms' => round($contextTime, 2),
                'summary_generation_ms' => round($summaryTime, 2),
                'total_time_ms' => round($totalTime, 2),
                'context_length' => strlen($summary),
                'farms_count' => count($context->farm_data['farms'] ?? []),
                'cache_used' => \Cache::has("chatbot_context_summary_{$user->id}_{$context->updated_at->timestamp}"),
            ],
            'optimization_status' => [
                'selective_loading' => 'enabled',
                'context_caching' => 'enabled',
                'reduced_history' => 'enabled',
                'fast_timeout' => 'enabled',
            ]
        ]);
    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'total_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
        ]);
    }
});

// Test avatar page
Route::get('/test-avatar-page', function() {
    return view('test-avatar');
});

// Test avatar route
Route::get('/test-avatar', function () {
    // Create a test user or get an existing one
    $user = \App\Models\User::first();

    if (!$user) {
        $user = \App\Models\User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>'
        ]);
    }

    // Get the avatar URL
    $avatarUrl = $user->profile_photo_url;

    return response()->json([
        'user_id' => $user->id,
        'user_name' => $user->name,
        'avatar_url' => $avatarUrl,
        'is_avataaars' => str_contains($avatarUrl, 'api.dicebear.com') && str_contains($avatarUrl, 'avataaars'),
        'has_dark_skin' => str_contains($avatarUrl, 'skinColor'),
        'has_hat' => str_contains($avatarUrl, 'top='),
        'has_african_styling' => str_contains($avatarUrl, 'skinColor') && str_contains($avatarUrl, 'top='),
        'is_dicebear' => str_contains($avatarUrl, 'api.dicebear.com')
    ]);
});

// SECURITY: Database fix route removed for security reasons
// This route allowed direct database manipulation and should never be exposed in production



// REMOVED: Duplicate Parametric Calculator Routes (moved to subscription middleware group)
// These routes are now properly handled in the subscription middleware group around line 1226

// Contact routes
Route::get('/contact', [\App\Http\Controllers\ContactController::class, 'index'])->name('contact.index');
Route::post('/contact', [\App\Http\Controllers\ContactController::class, 'submit'])->name('contact.submit');

// reCAPTCHA test routes
Route::get('/test-recaptcha', [\App\Http\Controllers\TestRecaptchaController::class, 'show'])->name('test.recaptcha');
Route::post('/test-recaptcha', [\App\Http\Controllers\TestRecaptchaController::class, 'submit'])->name('test.recaptcha.submit');

// reCAPTCHA debug route
Route::get('/debug-recaptcha', function() {
    $service = app(\App\Services\RecaptchaService::class);
    return response()->json([
        'enabled' => $service->isEnabled(),
        'site_key' => $service->getSiteKey(),
        'has_secret' => !empty($service->getSecretKey()),
        'min_score' => setting('recaptcha_min_score', 0.5),
        'script_tag' => $service->getScriptTag(),
        'html_sample' => $service->getHtml('contact'),
    ]);
});

// Newsletter routes
Route::post('/newsletter/subscribe', [\App\Http\Controllers\NewsletterController::class, 'subscribe'])->name('newsletter.subscribe');

// Redirect request-quote to contact page
Route::get('/request-quote', function() {
    return redirect()->route('contact.index');
})->name('request-quote');

// Redirect about to our-story page
Route::get('/about', function() {
    return redirect()->route('our-story');
})->name('about');

// Redirect careers to external URL
Route::get('/careers', function() {
    return redirect('https://admin.riwe.io/careers/********************************');
})->name('careers');

// Custom Pages
Route::get('/p/{slug}', [\App\Http\Controllers\PageController::class, 'show'])->name('pages.show');

// Events routes
Route::get('/events', [\App\Http\Controllers\EventController::class, 'index'])->name('events.index');
Route::get('/events/{slug}', [\App\Http\Controllers\EventController::class, 'show'])->name('events.show');

// Event registration routes
// Public routes for event registration
Route::post('/events/{event}/register', [\App\Http\Controllers\EventRegistrationController::class, 'register'])->name('events.register');
Route::post('/event-registrations/{registration}/cancel', [\App\Http\Controllers\EventRegistrationController::class, 'cancel'])->name('event-registrations.cancel');
Route::get('/tickets/{token}', [\App\Http\Controllers\EventRegistrationController::class, 'showTicket'])->name('event-registrations.ticket');
Route::get('/tickets/{token}/download', [\App\Http\Controllers\EventRegistrationController::class, 'downloadTicket'])->name('event-registrations.download-ticket');
Route::get('/tickets/{token}/apple-wallet', [\App\Http\Controllers\EventRegistrationController::class, 'appleWallet'])->name('event-registrations.apple-wallet');

// Authenticated user event routes
Route::middleware(['auth', 'block.developer'])->group(function () {
    Route::get('/my-events', [\App\Http\Controllers\EventRegistrationController::class, 'myEvents'])->name('my-events');
});

// Guest routes
Route::middleware('guest')->group(function () {
    // Login Routes

    Route::get('login', [AuthenticatedSessionController::class, 'create'])
        ->name('login');
    Route::post('login', [AuthenticatedSessionController::class, 'store']);

    // Two-factor verification route for login flow
    Route::post('two-factor/verify', [AuthenticatedSessionController::class, 'verifyTwoFactor'])
        ->name('two-factor.verify');

    // Password Reset Routes
    Route::get('forgot-password', [\App\Http\Controllers\Auth\PasswordResetLinkController::class, 'create'])
        ->name('password.request');
    Route::post('forgot-password', [\App\Http\Controllers\Auth\PasswordResetLinkController::class, 'store'])
        ->name('password.email');
    Route::get('reset-password/{token}', [\App\Http\Controllers\Auth\NewPasswordController::class, 'create'])
        ->name('password.reset');
    Route::post('reset-password', [\App\Http\Controllers\Auth\NewPasswordController::class, 'store'])
        ->name('password.store');

    // Admin Login Routes (Ghost Protocol)
    Route::middleware('ghost.protocol')->group(function() {
        Route::get('ghostprotocol', [\App\Http\Controllers\Admin\AdminLoginController::class, 'showLoginForm'])->name('admin.login');
        Route::post('ghostprotocol', [\App\Http\Controllers\Admin\AdminLoginController::class, 'login']);
    });

    // Admin Password Reset Routes
    Route::prefix('admin')->group(function() {
        Route::get('forgot-password', [\App\Http\Controllers\Admin\AdminPasswordResetController::class, 'showLinkRequestForm'])
            ->name('admin.password.request');
        Route::post('forgot-password', [\App\Http\Controllers\Admin\AdminPasswordResetController::class, 'sendResetLinkEmail'])
            ->name('admin.password.email');
        Route::get('reset-password/{token}', [\App\Http\Controllers\Admin\AdminPasswordResetController::class, 'showResetForm'])
            ->name('admin.password.reset');
        Route::post('reset-password', [\App\Http\Controllers\Admin\AdminPasswordResetController::class, 'reset'])
            ->name('admin.password.update');
    });

    // Registration Routes
    Route::get('register', [\App\Http\Controllers\Auth\RegisteredUserController::class, 'create'])
        ->middleware('registration.enabled')
        ->name('register');
    Route::post('register', [\App\Http\Controllers\Auth\RegisteredUserController::class, 'store'])
        ->middleware('registration.enabled');

    // No debug routes or special routes
});

// Include digital card routes
require __DIR__.'/digital-card.php';

// Include bookkeeping routes
require __DIR__.'/bookkeeping.php';

// Client Management Routes
Route::middleware(['auth', 'verified', \App\Http\Middleware\BusinessModuleMiddleware::class, 'block.developer'])->group(function () {
    Route::resource('clients', \App\Http\Controllers\ClientController::class);
});

// Agent BVN Verification Routes
Route::middleware(['auth', 'role:agent', 'block.developer'])->prefix('agent')->name('agent.')->group(function () {
    // BVN Verification
    Route::get('/users/{user}/bvn', [\App\Http\Controllers\Agent\BvnVerificationController::class, 'create'])->name('bvn.create');
    Route::post('/users/{user}/bvn', [\App\Http\Controllers\Agent\BvnVerificationController::class, 'store'])->name('bvn.store');
    Route::get('/users/{user}/bvn/status', [\App\Http\Controllers\Agent\BvnVerificationController::class, 'show'])->name('bvn.show');

    // Bank Account Management
    Route::get('/users/{user}/bank-accounts', [\App\Http\Controllers\Agent\BankAccountController::class, 'index'])->name('users.bank-accounts.index');
    Route::get('/users/{user}/bank-accounts/create', [\App\Http\Controllers\Agent\BankAccountController::class, 'create'])->name('users.bank-accounts.create');
    Route::post('/users/{user}/bank-accounts', [\App\Http\Controllers\Agent\BankAccountController::class, 'store'])->name('users.bank-accounts.store');
    Route::get('/users/{user}/bank-accounts/{bankAccount}/edit', [\App\Http\Controllers\Agent\BankAccountController::class, 'edit'])->name('users.bank-accounts.edit');
    Route::put('/users/{user}/bank-accounts/{bankAccount}', [\App\Http\Controllers\Agent\BankAccountController::class, 'update'])->name('users.bank-accounts.update');
    Route::delete('/users/{user}/bank-accounts/{bankAccount}', [\App\Http\Controllers\Agent\BankAccountController::class, 'destroy'])->name('users.bank-accounts.destroy');
    Route::put('/users/{user}/bank-accounts/{bankAccount}/set-default', [\App\Http\Controllers\Agent\BankAccountController::class, 'setDefault'])->name('users.bank-accounts.set-default');

    // REMOVED: Duplicate Rupa Digital Card Management routes (handled in agent.php)
    // These routes were conflicting with the routes defined in agent.php

    // Service Provider Requests
    Route::get('/service-provider-requests', [\App\Http\Controllers\Agent\ServiceProviderRequestController::class, 'index'])->name('service-provider-requests.index');
    Route::get('/service-provider-requests/create', [\App\Http\Controllers\Agent\ServiceProviderRequestController::class, 'create'])->name('service-provider-requests.create');
    Route::post('/service-provider-requests', [\App\Http\Controllers\Agent\ServiceProviderRequestController::class, 'store'])->name('service-provider-requests.store');
    Route::get('/service-provider-requests/{serviceProviderRequest}', [\App\Http\Controllers\Agent\ServiceProviderRequestController::class, 'show'])->name('service-provider-requests.show');
    Route::put('/service-provider-requests/{serviceProviderRequest}/cancel', [\App\Http\Controllers\Agent\ServiceProviderRequestController::class, 'cancel'])->name('service-provider-requests.cancel');
});

// Admin Support Ticket Routes (accessible by admin and manager)
Route::middleware(['auth', 'role:admin|manager'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/tickets', [\App\Http\Controllers\Admin\SupportTicketController::class, 'index'])->name('tickets.index');
    Route::get('/tickets/{ticket}', [\App\Http\Controllers\Admin\SupportTicketController::class, 'show'])->name('tickets.show');
    Route::put('/tickets/{ticket}', [\App\Http\Controllers\Admin\SupportTicketController::class, 'update'])->name('tickets.update');

    // REMOVED: Duplicate Rupa Dashboard Routes (handled in admin.php)
    // These routes were conflicting with the routes defined in admin.php
    Route::get('/rupa/access-logs', [\App\Http\Controllers\Admin\RupaDashboardController::class, 'accessLogs'])->name('rupa.access-logs');
    Route::get('/rupa/payment-requests', [\App\Http\Controllers\Admin\RupaDashboardController::class, 'paymentRequests'])->name('rupa.payment-requests');
    Route::get('/rupa/card-styles', [\App\Http\Controllers\Admin\RupaDashboardController::class, 'cardStyles'])->name('rupa.card-styles');
    Route::get('/rupa/card-styles/create', [\App\Http\Controllers\Admin\RupaDashboardController::class, 'createCardStyle'])->name('rupa.card-styles.create');
    Route::post('/rupa/card-styles', [\App\Http\Controllers\Admin\RupaDashboardController::class, 'storeCardStyle'])->name('rupa.card-styles.store');
    Route::get('/rupa/card-styles/{style}/edit', [\App\Http\Controllers\Admin\RupaDashboardController::class, 'editCardStyle'])->name('rupa.card-styles.edit');
    Route::put('/rupa/card-styles/{style}', [\App\Http\Controllers\Admin\RupaDashboardController::class, 'updateCardStyle'])->name('rupa.card-styles.update');
    Route::put('/rupa/card-styles/{style}/set-default', [\App\Http\Controllers\Admin\RupaDashboardController::class, 'setDefaultCardStyle'])->name('rupa.card-styles.set-default');
    Route::put('/rupa/card-styles/{style}/activate', [\App\Http\Controllers\Admin\RupaDashboardController::class, 'activateCardStyle'])->name('rupa.card-styles.activate');
    Route::put('/rupa/card-styles/{style}/deactivate', [\App\Http\Controllers\Admin\RupaDashboardController::class, 'deactivateCardStyle'])->name('rupa.card-styles.deactivate');
    Route::put('/rupa/payment-requests/{request}/approve', [\App\Http\Controllers\Admin\RupaDashboardController::class, 'approvePaymentRequest'])->name('rupa.payment-requests.approve');
    Route::put('/rupa/payment-requests/{request}/reject', [\App\Http\Controllers\Admin\RupaDashboardController::class, 'rejectPaymentRequest'])->name('rupa.payment-requests.reject');
    Route::put('/rupa/payment-requests/{request}/complete', [\App\Http\Controllers\Admin\RupaDashboardController::class, 'completePaymentRequest'])->name('rupa.payment-requests.complete');
    Route::post('/tickets/{ticket}/reply', [\App\Http\Controllers\Admin\SupportTicketController::class, 'reply'])->name('tickets.reply');
    Route::put('/tickets/{ticket}/close', [\App\Http\Controllers\Admin\SupportTicketController::class, 'close'])->name('tickets.close');
    Route::put('/tickets/{ticket}/reopen', [\App\Http\Controllers\Admin\SupportTicketController::class, 'reopen'])->name('tickets.reopen');
    Route::put('/tickets/{ticket}/assign', [\App\Http\Controllers\Admin\SupportTicketController::class, 'assign'])->name('tickets.assign');
});

// Include debug routes
require __DIR__.'/debug.php';

// Include chatbot routes
require __DIR__.'/chatbot.php';

// Include Web3 routes
require __DIR__.'/web3.php';

// Notification tracking routes (public)
Route::get('/n/open', [\App\Http\Controllers\NotificationTrackingController::class, 'trackOpen'])->name('notifications.track.open');
Route::get('/n/click', [\App\Http\Controllers\NotificationTrackingController::class, 'trackClick'])->name('notifications.track.click');

// Notification preferences routes (authenticated)
Route::middleware(['auth', 'block.developer'])->group(function () {
    Route::get('/profile/notification-preferences', [\App\Http\Controllers\NotificationPreferenceController::class, 'index'])->name('profile.notification-preferences');
    Route::post('/profile/notification-preferences', [\App\Http\Controllers\NotificationPreferenceController::class, 'update'])->name('profile.notification-preferences.update');
    Route::post('/profile/notification-preferences/toggle-type', [\App\Http\Controllers\NotificationPreferenceController::class, 'toggleType'])->name('profile.notification-preferences.toggle-type');
    Route::post('/profile/notification-preferences/update-channels', [\App\Http\Controllers\NotificationPreferenceController::class, 'updateChannels'])->name('profile.notification-preferences.update-channels');
    Route::post('/profile/notification-preferences/update-quiet-hours', [\App\Http\Controllers\NotificationPreferenceController::class, 'updateQuietHours'])->name('profile.notification-preferences.update-quiet-hours');
    Route::post('/notifications/track-action', [\App\Http\Controllers\NotificationTrackingController::class, 'trackAction'])->name('notifications.track.action');
});

// Admin notification analytics routes
Route::middleware(['auth', 'role:admin'])->group(function () {
    Route::get('/admin/notifications/analytics', [\App\Http\Controllers\NotificationTrackingController::class, 'getAnalytics'])->name('admin.notifications.analytics');
});

// Public invoice routes
Route::get('/invoices/public/{invoice:id}/{hash}', [\App\Http\Controllers\PublicInvoiceController::class, 'show'])
    ->name('invoices.public.show');
Route::get('/invoices/public/{invoice:id}/{hash}/pdf', [\App\Http\Controllers\PublicInvoiceController::class, 'generatePdf'])
    ->name('invoices.public.pdf');
Route::post('/invoices/public/{invoice:id}/{hash}/pay', [\App\Http\Controllers\PublicInvoiceController::class, 'processPayment'])
    ->name('invoices.public.pay');

// Authenticated routes
Route::middleware(['auth'])->group(function () {
    // Remove these duplicate two-factor routes
    // Route::post('/two-factor/enable', [TwoFactorAuthController::class, 'enable'])
    //     ->name('two-factor.enable');
    // Route::post('/two-factor/disable', [TwoFactorAuthController::class, 'disable'])
    //     ->name('two-factor.disable');

    // Dashboard
    // Notices API
    Route::get('/notices', [NoticeController::class, 'getActiveNotices'])->name('notices.active');
    Route::post('/notices/{notice}/viewed', [NoticeController::class, 'markAsViewed'])->name('notices.viewed');

    // Tour API
    Route::get('/tours/{tourName}', [\App\Http\Controllers\TourController::class, 'status'])->name('tours.status');
    Route::post('/tours/{tourName}', [\App\Http\Controllers\TourController::class, 'update'])->name('tours.update');
    Route::post('/tours/{tourName}/complete', [\App\Http\Controllers\TourController::class, 'complete'])->name('tours.complete');
    Route::post('/tours/{tourName}/reset', [\App\Http\Controllers\TourController::class, 'reset'])->name('tours.reset');

    Route::get('/dashboard', [DashboardController::class, 'index'])->middleware(['auth', \App\Http\Middleware\TwoFactorAuth::class, 'block.non.user', 'block.developer'])->name('dashboard');

    // Admin Routes - AdminAccessControl middleware removed to prevent 404 errors
    Route::prefix('admin')->middleware(['auth', 'role:admin'])->group(function () {
        Route::post('/logout', [\App\Http\Controllers\Admin\AdminLoginController::class, 'logout'])->name('admin.logout');

        // Admin Two-Factor Authentication Routes (before 2FA middleware)
        Route::prefix('two-factor')->name('admin.two-factor.')->group(function () {
            Route::get('/setup', [\App\Http\Controllers\Admin\TwoFactorAuthController::class, 'setup'])->name('setup');
            Route::post('/enable', [\App\Http\Controllers\Admin\TwoFactorAuthController::class, 'enable'])->name('enable');
            Route::post('/disable', [\App\Http\Controllers\Admin\TwoFactorAuthController::class, 'disable'])->name('disable');
            Route::get('/confirm', [\App\Http\Controllers\Admin\TwoFactorAuthController::class, 'confirmForm'])->name('confirm');
            Route::post('/confirm', [\App\Http\Controllers\Admin\TwoFactorAuthController::class, 'confirm'])->name('confirm.post');
            Route::get('/recovery-codes', [\App\Http\Controllers\Admin\TwoFactorAuthController::class, 'showRecoveryCodes'])->name('recovery-codes');
            Route::post('/recovery-codes/regenerate', [\App\Http\Controllers\Admin\TwoFactorAuthController::class, 'regenerateRecoveryCodes'])->name('recovery-codes.regenerate');
        });

        // Protected admin routes (require 2FA if enabled)
        Route::middleware('admin.2fa')->group(function () {
            Route::get('/dashboard', [DashboardController::class, 'adminDashboard'])->name('admin.dashboard');

        // Crop Management
        Route::resource('crops', \App\Http\Controllers\Admin\CropController::class)->names('admin.crops');

        // User Management
        Route::get('users/export', [\App\Http\Controllers\Admin\UserController::class, 'export'])->name('admin.users.export');
        Route::resource('users', \App\Http\Controllers\Admin\UserController::class)->names('admin.users');
        Route::post('users/{user}/verify-email', [\App\Http\Controllers\Admin\UserController::class, 'verifyEmail'])->name('admin.users.verify-email');
        Route::post('users/{user}/topup-wallet', [\App\Http\Controllers\Admin\UserController::class, 'topupWallet'])->name('admin.users.topup-wallet');
        Route::post('users/{user}/debit-wallet', [\App\Http\Controllers\Admin\UserController::class, 'debitWallet'])->name('admin.users.debit-wallet');
        Route::get('users/{user}/virtual-accounts/{virtualAccount}', [\App\Http\Controllers\Admin\UserController::class, 'showVirtualAccount'])->name('admin.users.virtual-accounts.show');

        // Transactions
        Route::get('transactions', [\App\Http\Controllers\Admin\TransactionController::class, 'index'])->name('admin.transactions.index');
        Route::get('transactions/{transaction}', [\App\Http\Controllers\Admin\TransactionController::class, 'show'])->name('admin.transactions.show');

        // Farms
        Route::resource('farms', \App\Http\Controllers\Admin\FarmController::class)->names('admin.farms');

        // Loan Management
        Route::get('loans/settings', [\App\Http\Controllers\Admin\LoanSettingsController::class, 'index'])->name('admin.loans.settings');
        Route::post('loans/settings/rcp-weights', [\App\Http\Controllers\Admin\LoanSettingsController::class, 'updateRcpWeights'])->name('admin.loans.settings.rcp-weights');
        Route::post('loans/settings/base-amounts', [\App\Http\Controllers\Admin\LoanSettingsController::class, 'updateBaseAmounts'])->name('admin.loans.settings.base-amounts');
        Route::post('loans/settings/rating-thresholds', [\App\Http\Controllers\Admin\LoanSettingsController::class, 'updateRatingThresholds'])->name('admin.loans.settings.rating-thresholds');
        Route::post('loans/settings/processing-fee', [\App\Http\Controllers\Admin\LoanSettingsController::class, 'updateProcessingFee'])->name('admin.loans.settings.processing-fee');
        Route::resource('loans', \App\Http\Controllers\Admin\LoanController::class)->names('admin.loans');
        Route::post('loans/{loan}/approve', [\App\Http\Controllers\Admin\LoanController::class, 'approve'])->name('admin.loans.approve');
        Route::post('loans/{loan}/reject', [\App\Http\Controllers\Admin\LoanController::class, 'reject'])->name('admin.loans.reject');

        Route::post('loans/interest-rate', [\App\Http\Controllers\Admin\LoanController::class, 'updateInterestRate'])->name('admin.loans.interest-rate.update');
        Route::post('loans/calculate-score/{user}', [\App\Http\Controllers\Admin\LoanController::class, 'calculateScore'])->name('admin.loans.calculate-score');



        // Bank Management
        Route::resource('banks', \App\Http\Controllers\Admin\BankController::class)->names('admin.banks');
        Route::post('banks/{bank}/status', [\App\Http\Controllers\Admin\BankController::class, 'updateStatus'])->name('admin.banks.update-status');
        Route::post('banks/{bank}/topup-wallet', [\App\Http\Controllers\Admin\BankController::class, 'topupWallet'])->name('admin.banks.topup-wallet');
        Route::post('banks/{bank}/debit-wallet', [\App\Http\Controllers\Admin\BankController::class, 'debitWallet'])->name('admin.banks.debit-wallet');


        // KYC Management
        Route::resource('kyc', \App\Http\Controllers\Admin\KYCController::class)->names('admin.kyc');
        Route::post('kyc/{kyc}/approve', [\App\Http\Controllers\Admin\KYCController::class, 'approve'])->name('admin.kyc.approve');
        Route::post('kyc/{kyc}/reject', [\App\Http\Controllers\Admin\KYCController::class, 'reject'])->name('admin.kyc.reject');
        Route::get('kyc/{kyc}/document/{document}', [\App\Http\Controllers\Admin\KYCController::class, 'getDocument'])->name('admin.kyc.document');

        // Trial Management
        Route::get('trials', [\App\Http\Controllers\Admin\TrialController::class, 'index'])->name('admin.trials.index');
        Route::get('trials/create', [\App\Http\Controllers\Admin\TrialController::class, 'create'])->name('admin.trials.create');
        Route::post('trials', [\App\Http\Controllers\Admin\TrialController::class, 'store'])->name('admin.trials.store');
        Route::get('trials/{trial}/edit', [\App\Http\Controllers\Admin\TrialController::class, 'edit'])->name('admin.trials.edit');
        Route::put('trials/{trial}', [\App\Http\Controllers\Admin\TrialController::class, 'update'])->name('admin.trials.update');
        Route::delete('trials/{trial}', [\App\Http\Controllers\Admin\TrialController::class, 'end'])->name('admin.trials.end');

        // Settings
        // Settings Routes
        Route::get('settings', [\App\Http\Controllers\Admin\SettingController::class, 'index'])->name('admin.settings.index');
        Route::post('settings', [\App\Http\Controllers\Admin\SettingController::class, 'update'])->name('admin.settings.update');
        Route::post('settings/toggle-invite-code', [\App\Http\Controllers\Admin\InviteCodeController::class, 'toggleRequireInviteCode'])->name('admin.settings.toggle-invite-code');
        Route::post('settings/test-email', [\App\Http\Controllers\Admin\SettingController::class, 'sendTestEmail'])->name('admin.settings.test-email');
        Route::post('settings/backup', [\App\Http\Controllers\Admin\SettingController::class, 'createBackup'])->name('admin.settings.backup');
        Route::get('settings/backup/{filename}', [\App\Http\Controllers\Admin\SettingController::class, 'downloadBackup'])->name('admin.settings.backup.download');
        Route::delete('settings/backup/{filename}', [\App\Http\Controllers\Admin\SettingController::class, 'deleteBackup'])->name('admin.settings.backup.delete');
        Route::post('settings/backup/restore/{filename}', [\App\Http\Controllers\Admin\SettingController::class, 'restoreBackup'])->name('admin.settings.backup.restore');
        Route::post('settings/backup/restore-latest', [\App\Http\Controllers\Admin\SettingController::class, 'restoreLatestBackup'])->name('admin.settings.backup.restore-latest');
        Route::post('settings/clear-cache', [\App\Http\Controllers\Admin\SettingController::class, 'clearCache'])->name('admin.settings.clear-cache');

        // Social Media Management
        Route::get('social-media', [\App\Http\Controllers\Admin\SocialMediaController::class, 'index'])->name('admin.social-media.index');
        Route::post('social-media', [\App\Http\Controllers\Admin\SocialMediaController::class, 'update'])->name('admin.social-media.update');

        // Coverage Regions
        Route::resource('coverage-regions', \App\Http\Controllers\Admin\CoverageRegionController::class)->names('admin.coverage-regions');
        Route::post('coverage-regions/{coverageRegion}/toggle-active', [\App\Http\Controllers\Admin\CoverageRegionController::class, 'toggleActive'])->name('admin.coverage-regions.toggle-active');

        // Payment Settings Routes
        Route::get('settings/payment', [\App\Http\Controllers\Admin\PaymentSettingsController::class, 'index'])->name('admin.settings.payment');
        Route::post('settings/payment', [\App\Http\Controllers\Admin\PaymentSettingsController::class, 'update'])->name('admin.settings.payment.update');
        Route::post('settings/payment/test-paystack', [\App\Http\Controllers\Admin\PaymentSettingsController::class, 'testPaystack'])->name('admin.settings.payment.test-paystack');

        // Payout Routes
        Route::prefix('payouts')->name('admin.payouts.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\PayoutController::class, 'index'])->name('index');
            Route::get('/withdrawals', [\App\Http\Controllers\Admin\PayoutController::class, 'withdrawals'])->name('withdrawals');
            Route::get('/payment-requests', [\App\Http\Controllers\Admin\PayoutController::class, 'paymentRequests'])->name('payment-requests');
            Route::get('/bulk', [\App\Http\Controllers\Admin\PayoutController::class, 'bulkPayoutForm'])->name('bulk');
            Route::post('/bulk', [\App\Http\Controllers\Admin\PayoutController::class, 'processBulkPayout'])->name('bulk.process');
            Route::get('/bulk/confirm', [\App\Http\Controllers\Admin\PayoutController::class, 'confirmBulkPayout'])->name('bulk.confirm');
            Route::post('/bulk/confirm', [\App\Http\Controllers\Admin\PayoutController::class, 'processBulkConfirmed'])->name('bulk.confirm.process');
            Route::get('/bulk/verify-otp', [\App\Http\Controllers\Admin\PayoutController::class, 'showBulkOtpForm'])->name('bulk.verify-otp');
            Route::post('/bulk/verify-otp', [\App\Http\Controllers\Admin\PayoutController::class, 'verifyBulkOtp'])->name('bulk.verify-otp.process');

            // Transfer OTP routes for withdrawals
            Route::post('/withdrawals/{transaction}/generate-otp', [\App\Http\Controllers\Admin\TransferOtpController::class, 'generateWithdrawalOtp'])->name('withdrawals.generate-otp');
            Route::get('/withdrawals/{transaction}/verify-otp', [\App\Http\Controllers\Admin\TransferOtpController::class, 'showWithdrawalOtpForm'])->name('withdrawals.verify-otp');
            Route::post('/withdrawals/{transaction}/verify-otp', [\App\Http\Controllers\Admin\TransferOtpController::class, 'verifyWithdrawalOtp'])->name('withdrawals.verify-otp.process');

            // Transfer OTP routes for payment requests
            Route::post('/payment-requests/{paymentRequest}/generate-otp', [\App\Http\Controllers\Admin\TransferOtpController::class, 'generatePaymentRequestOtp'])->name('payment-requests.generate-otp');
            Route::get('/payment-requests/{paymentRequest}/verify-otp', [\App\Http\Controllers\Admin\TransferOtpController::class, 'showPaymentRequestOtpForm'])->name('payment-requests.verify-otp');
            Route::post('/payment-requests/{paymentRequest}/verify-otp', [\App\Http\Controllers\Admin\TransferOtpController::class, 'verifyPaymentRequestOtp'])->name('payment-requests.verify-otp.process');

            // Transfer management routes
            Route::get('/transfers', [\App\Http\Controllers\Admin\TransferOtpController::class, 'listTransfers'])->name('transfers');
            Route::get('/transfers/{transferCode}/details', [\App\Http\Controllers\Admin\TransferOtpController::class, 'showTransferDetails'])->name('transfers.details');
            Route::get('/transfers/{transaction}/verify', [\App\Http\Controllers\Admin\TransferOtpController::class, 'verifyTransferStatus'])->name('transfers.verify');

            // Paystack balance route
            Route::get('/balance', [\App\Http\Controllers\Admin\TransferOtpController::class, 'getPaystackBalance'])->name('balance');

            // Settings routes
            Route::get('/settings', [\App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('settings.index');
            Route::post('/settings', [\App\Http\Controllers\Admin\SettingsController::class, 'update'])->name('settings.update');
            Route::get('/settings/verify-paystack', [\App\Http\Controllers\Admin\SettingsController::class, 'verifyPaystackConfig'])->name('settings.verify-paystack');

            // Legacy routes (will be modified to use OTP flow)
            Route::post('/withdrawals/{transaction}', [\App\Http\Controllers\Admin\PayoutController::class, 'processWithdrawal'])->name('withdrawals.process');
            Route::post('/payment-requests/{paymentRequest}', [\App\Http\Controllers\Admin\PayoutController::class, 'processPaymentRequest'])->name('payment-requests.process');
        });

        // Insurance Routes
        Route::prefix('insurance')->name('admin.insurance.')->group(function () {
            // Insurance Overview
            Route::get('/overview', [\App\Http\Controllers\Admin\InsuranceOverviewController::class, 'index'])->name('overview');

            // Insurance Payout Routes
            Route::prefix('payouts')->name('payouts.')->group(function () {
                Route::get('/', [\App\Http\Controllers\Admin\InsurancePayoutController::class, 'index'])->name('index');
                Route::get('/company/{company}', [\App\Http\Controllers\Admin\InsurancePayoutController::class, 'companyPayouts'])->name('company');
                Route::post('/process', [\App\Http\Controllers\Admin\InsurancePayoutController::class, 'processPayout'])->name('process');
                Route::get('/verify/{reference}', [\App\Http\Controllers\Admin\InsurancePayoutController::class, 'verifyPayout'])->name('verify');
                Route::post('/update-commission', [\App\Http\Controllers\Admin\InsurancePayoutController::class, 'updateCommissionRate'])->name('update-commission');
            });
        });

        // Revenue Dashboard
        Route::prefix('revenue')->name('admin.revenue.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\RevenueController::class, 'index'])->name('index');
            Route::get('/category/{category}', [\App\Http\Controllers\Admin\RevenueController::class, 'categoryDetail'])->name('category');
        });

        // Analytics
        Route::get('analytics', [\App\Http\Controllers\Admin\AnalyticsController::class, 'index'])->name('admin.analytics.index');
        Route::get('analytics/export/{format?}', [\App\Http\Controllers\Admin\AnalyticsController::class, 'export'])->name('admin.analytics.export');

        // Notices
        Route::resource('notices', \App\Http\Controllers\Admin\NoticeController::class)->names('admin.notices');
        Route::post('notices/{notice}/toggle-active', [\App\Http\Controllers\Admin\NoticeController::class, 'toggleActive'])->name('admin.notices.toggle-active');

        // Weather Alerts
        Route::get('weather-alerts', [\App\Http\Controllers\WeatherAlertController::class, 'adminIndex'])->name('admin.weather-alerts.index');
        Route::get('weather-alerts/create', [\App\Http\Controllers\WeatherAlertController::class, 'create'])->name('admin.weather-alerts.create');
        Route::post('weather-alerts', [\App\Http\Controllers\WeatherAlertController::class, 'store'])->name('admin.weather-alerts.store');
        Route::get('weather-alerts/{alert}', [\App\Http\Controllers\WeatherAlertController::class, 'adminShow'])->name('admin.weather-alerts.show');
        Route::get('weather-alerts/{alert}/edit', [\App\Http\Controllers\WeatherAlertController::class, 'edit'])->name('admin.weather-alerts.edit');
        Route::put('weather-alerts/{alert}', [\App\Http\Controllers\WeatherAlertController::class, 'update'])->name('admin.weather-alerts.update');
        Route::delete('weather-alerts/{alert}', [\App\Http\Controllers\WeatherAlertController::class, 'destroy'])->name('admin.weather-alerts.destroy');

        // Weather Alert Analytics
        Route::get('weather-alerts/analytics', [\App\Http\Controllers\Admin\WeatherAlertAnalyticsController::class, 'index'])->name('admin.weather-alerts.analytics');
        Route::get('weather-alerts/analytics/export', [\App\Http\Controllers\Admin\WeatherAlertAnalyticsController::class, 'export'])->name('admin.weather-alerts.analytics.export');
        Route::get('weather-alerts/analytics/{id}', [\App\Http\Controllers\Admin\WeatherAlertAnalyticsController::class, 'showAlert'])->name('admin.weather-alerts.analytics.alert');

        // REMOVED: Duplicate Support Tickets routes (handled by SupportTicketController above)
        // These routes were conflicting with the SupportTicketController routes defined earlier

        // Fields Management
        Route::resource('fields', \App\Http\Controllers\Admin\FieldController::class)->names('admin.fields');
        Route::get('fields/pending', [\App\Http\Controllers\Admin\FieldController::class, 'pending'])->name('admin.fields.pending');
        Route::post('fields/{field}/reject', [\App\Http\Controllers\Admin\FieldController::class, 'reject'])->name('admin.fields.reject');

        // Approvals
        Route::get('approvals', [\App\Http\Controllers\Admin\ApprovalController::class, 'index'])->name('admin.approvals.index');
        Route::post('approvals/{id}/approve', [\App\Http\Controllers\Admin\ApprovalController::class, 'approve'])->name('admin.approvals.approve');
        Route::post('approvals/{id}/reject', [\App\Http\Controllers\Admin\ApprovalController::class, 'reject'])->name('admin.approvals.reject');

        // Invite Codes
        Route::resource('invite-codes', \App\Http\Controllers\Admin\InviteCodeController::class)->names('admin.invite-codes');
        Route::post('invite-codes/{inviteCode}/send-email', [\App\Http\Controllers\Admin\InviteCodeController::class, 'sendEmail'])->name('admin.invite-codes.send-email');

        // Service Providers Management
        Route::resource('service-providers', \App\Http\Controllers\Admin\ServiceProviderController::class)->names('admin.service-providers');

        // Service Bookings Management
        Route::resource('service-bookings', \App\Http\Controllers\Admin\ServiceBookingController::class)->names('admin.service-bookings');
        Route::post('service-bookings/{serviceBooking}/update-status', [\App\Http\Controllers\Admin\ServiceBookingController::class, 'updateStatus'])->name('admin.service-bookings.update-status');
        Route::post('service-bookings/{serviceBooking}/add-notes', [\App\Http\Controllers\Admin\ServiceBookingController::class, 'addNotes'])->name('admin.service-bookings.add-notes');
        Route::post('service-bookings/{serviceBooking}/reassign', [\App\Http\Controllers\Admin\ServiceBookingController::class, 'reassign'])->name('admin.service-bookings.reassign');

        // Insurance Products Management
        Route::resource('insurance-products', \App\Http\Controllers\Admin\InsuranceProductController::class)->names('admin.insurance-products');

        // Insurance Companies Management
        Route::resource('insurance-companies', \App\Http\Controllers\Admin\InsuranceCompanyController::class)->names('admin.insurance-companies');
        Route::post('insurance-companies/{insuranceCompany}/update-commission', [\App\Http\Controllers\Admin\InsuranceCompanyController::class, 'updateCommission'])->name('admin.insurance-companies.update-commission');
        Route::post('insurance-companies/{insuranceCompany}/change-status', [\App\Http\Controllers\Admin\InsuranceCompanyController::class, 'changeStatus'])->name('admin.insurance-companies.change-status');

        // Agents Management
        Route::resource('agents', \App\Http\Controllers\Admin\AgentController::class)->names('admin.agents');

        // Page Content Management
        // Landing Page Management
        Route::get('/landing-page', [\App\Http\Controllers\Admin\LandingPageController::class, 'index'])->name('admin.landing-page.index');
        Route::get('/landing-page/create/{page}', [\App\Http\Controllers\Admin\LandingPageController::class, 'create'])->name('admin.landing-page.create');
        Route::post('/landing-page', [\App\Http\Controllers\Admin\LandingPageController::class, 'store'])->name('admin.landing-page.store');
        Route::get('/landing-page/{id}/edit', [\App\Http\Controllers\Admin\LandingPageController::class, 'edit'])->name('admin.landing-page.edit');
        Route::put('/landing-page/{id}', [\App\Http\Controllers\Admin\LandingPageController::class, 'update'])->name('admin.landing-page.update');
        Route::post('/landing-page/reorder', [\App\Http\Controllers\Admin\LandingPageController::class, 'reorder'])->name('admin.landing-page.reorder');

        // Solutions Page Management
        Route::get('/solutions-page', [\App\Http\Controllers\Admin\LandingPageController::class, 'solutionsIndex'])->name('admin.solutions-page.index');
        Route::post('/solutions-page/reorder', [\App\Http\Controllers\Admin\LandingPageController::class, 'reorder'])->name('admin.solutions-page.reorder');

        // Our Story Page Management
        Route::get('/our-story-page', [\App\Http\Controllers\Admin\LandingPageController::class, 'ourStoryIndex'])->name('admin.our-story-page.index');
        Route::post('/our-story-page/reorder', [\App\Http\Controllers\Admin\LandingPageController::class, 'reorder'])->name('admin.our-story-page.reorder');

        // Use Cases Page Management
        Route::get('/use-cases-page', [\App\Http\Controllers\Admin\LandingPageController::class, 'useCasesIndex'])->name('admin.use-cases-page.index');
        Route::post('/use-cases-page/reorder', [\App\Http\Controllers\Admin\LandingPageController::class, 'reorder'])->name('admin.use-cases-page.reorder');

        // Events Page Management
        Route::get('/events-page', [\App\Http\Controllers\Admin\LandingPageController::class, 'eventsIndex'])->name('admin.events-page.index');
        Route::post('/events-page/reorder', [\App\Http\Controllers\Admin\LandingPageController::class, 'reorder'])->name('admin.events-page.reorder');

        // Custom Pages Management
        Route::resource('pages', \App\Http\Controllers\Admin\PageController::class)->names('admin.pages');
        Route::post('pages/{id}/toggle-publish', [\App\Http\Controllers\Admin\PageController::class, 'togglePublish'])->name('admin.pages.toggle-publish');

        // Page Sections Management
        Route::resource('page-sections', \App\Http\Controllers\Admin\PageSectionController::class)->names('admin.page-sections');
        Route::post('page-sections/reorder', [\App\Http\Controllers\Admin\PageSectionController::class, 'reorder'])->name('admin.page-sections.reorder');

        // Page Themes Management
        Route::resource('page-themes', \App\Http\Controllers\Admin\PageThemeController::class)->names('admin.page-themes');

        // Events Management
        Route::resource('events', \App\Http\Controllers\Admin\EventController::class)->names('admin.events');

        // Event Banner Ads Management - Routes moved to admin-events.php to avoid conflicts

        // Event Partner Logos Management - Routes moved to admin-events.php to avoid conflicts

        // Event Registrations Management - Routes moved to admin-events.php to avoid conflicts



        // Duplicate solutions-page routes removed - already defined above

        // Subscriptions Management
        Route::get('/subscriptions', [\App\Http\Controllers\Admin\SubscriptionController::class, 'index'])->name('admin.subscriptions.index');
        Route::get('/subscriptions/create', [\App\Http\Controllers\Admin\SubscriptionController::class, 'create'])->name('admin.subscriptions.create');
        Route::post('/subscriptions', [\App\Http\Controllers\Admin\SubscriptionController::class, 'store'])->name('admin.subscriptions.store');
        Route::get('/subscriptions/{plan}/edit', [\App\Http\Controllers\Admin\SubscriptionController::class, 'edit'])->name('admin.subscriptions.edit');
        Route::put('/subscriptions/{plan}', [\App\Http\Controllers\Admin\SubscriptionController::class, 'update'])->name('admin.subscriptions.update');
        Route::delete('/subscriptions/{plan}', [\App\Http\Controllers\Admin\SubscriptionController::class, 'destroy'])->name('admin.subscriptions.destroy');
        Route::post('/subscriptions/update-business-module-price', [\App\Http\Controllers\Admin\SubscriptionController::class, 'updateBusinessModulePrice'])->name('admin.subscriptions.update-business-module-price');

        // Subscription Features
        Route::get('/subscriptions/features', [\App\Http\Controllers\Admin\SubscriptionController::class, 'features'])->name('admin.subscriptions.features');
        Route::post('/subscriptions/features', [\App\Http\Controllers\Admin\SubscriptionController::class, 'storeFeature'])->name('admin.subscriptions.features.store');
        Route::put('/subscriptions/features/{feature}', [\App\Http\Controllers\Admin\SubscriptionController::class, 'updateFeature'])->name('admin.subscriptions.features.update');
        Route::delete('/subscriptions/features/{feature}', [\App\Http\Controllers\Admin\SubscriptionController::class, 'destroyFeature'])->name('admin.subscriptions.features.destroy');

        // User Subscriptions
        Route::get('/subscriptions/users', [\App\Http\Controllers\Admin\SubscriptionController::class, 'userSubscriptions'])->name('admin.subscriptions.users');
        Route::get('/subscriptions/assign', [\App\Http\Controllers\Admin\SubscriptionController::class, 'assignSubscription'])->name('admin.subscriptions.assign');
        Route::post('/subscriptions/users', [\App\Http\Controllers\Admin\SubscriptionController::class, 'storeUserSubscription'])->name('admin.subscriptions.users.store');
        Route::put('/subscriptions/users/{subscription}/cancel', [\App\Http\Controllers\Admin\SubscriptionController::class, 'cancelUserSubscription'])->name('admin.subscriptions.users.cancel');
        });
    });

    // Farms - Basic routes
    Route::resource('farms', FarmController::class);

    // Premium farm features with subscription check
    Route::middleware(['subscription'])->group(function () {
        Route::get('/farms/{farm}/weather', [FarmController::class, 'weather'])->name('farms.weather');
        Route::get('/farms/{farm}/risk', [FarmController::class, 'risk'])->name('farms.risk');
        Route::get('/farms/{farm}/insights', [FarmController::class, 'insights'])->name('farms.insights');
        Route::get('/farms/{farm}/analytics', [FarmController::class, 'analytics'])->name('farms.analytics');
        Route::get('/farms/{farm}/ndvi', [FarmController::class, 'ndvi'])->name('farms.ndvi');
        Route::get('/farms/{farm}/assess-risk', [FarmController::class, 'assessRisk'])->name('farms.assess-risk');
        Route::post('/farms/{farm}/assess-risk', [FarmController::class, 'assessRisk']);
        Route::get('farms/{farm}/risk-analysis', [FarmController::class, 'riskAnalysis'])->name('farms.risk-analysis');

        // Crop Calendar routes
        Route::get('/farms/{farm}/calendar', [\App\Http\Controllers\CropCalendarController::class, 'show'])->name('farms.calendar');
        Route::get('/farms/{farm}/calendar/data', [\App\Http\Controllers\CropCalendarController::class, 'getCalendarData'])->name('farms.calendar.data');
        Route::post('/farms/{farm}/calendar/activities', [\App\Http\Controllers\CropCalendarController::class, 'createActivities'])->name('farms.calendar.activities');
        Route::post('/farms/{farm}/calendar/planting-date', [\App\Http\Controllers\CropCalendarController::class, 'updatePlantingDate'])->name('farms.calendar.planting-date');

        // Test route for calendar data
        Route::get('/farms/{farm}/calendar/test-data', function(\App\Models\Farm $farm) {
            $service = app(\App\Services\CropCalendarService::class);
            $calendar = $service->generateCropCalendar($farm);
            return response()->json([
                'success' => true,
                'calendar' => $calendar,
                'activities_count' => count($calendar['activities']),
                'activities_sample' => array_slice($calendar['activities'], 0, 3)
            ]);
        });
    });

    // Farm Activities
    Route::get('/farm-activities/calendar', [\App\Http\Controllers\FarmActivityController::class, 'calendar'])->name('farm-activities.calendar');
    Route::get('/farm-activities/analytics', [\App\Http\Controllers\FarmActivityController::class, 'analytics'])->name('farm-activities.analytics');
    Route::resource('farm-activities', \App\Http\Controllers\FarmActivityController::class);

    // Crop Insights - Protected by subscription middleware
    Route::middleware('subscription')->group(function () {
        Route::get('/cropinsights', [CropInsightsController::class, 'index'])->name('cropinsights.index');
        Route::get('/cropinsights/{farm}', [CropInsightsController::class, 'getFarmInsights'])->name('cropinsights.farm');
    });

    // Market Analysis
    Route::prefix('market-analysis')->group(function () {
        Route::get('/', [MarketAnalysisController::class, 'index'])->name('market-analysis.index');
        Route::get('/crop-data', [MarketAnalysisController::class, 'getCropData'])->name('market-analysis.crop-data');
        Route::post('/export-pdf', [MarketAnalysisController::class, 'exportPdf'])->name('market-analysis.export-pdf');
        Route::get('/download-pdf', [MarketAnalysisController::class, 'downloadPdf'])->name('market-analysis.download-pdf');
        Route::post('/email-report', [MarketAnalysisController::class, 'emailReport'])->name('market-analysis.email-report');
        Route::post('/save-preferences', [MarketAnalysisController::class, 'savePreferences'])->name('market-analysis.save-preferences');
    });

    // Market Data API routes
    Route::prefix('api/market-data')->group(function () {
        Route::get('/', [\App\Http\Controllers\MarketDataController::class, 'getMarketData'])->name('api.market-data');
        Route::get('/historical', [\App\Http\Controllers\MarketDataController::class, 'getHistoricalData'])->name('api.market-data.historical');
        Route::get('/trend', [\App\Http\Controllers\MarketDataController::class, 'getTrendData'])->name('api.market-data.trend');
    });



    // Location tracking routes
    Route::post('/store-location', [\App\Http\Controllers\LocationController::class, 'storeLocation'])->name('location.store');
    Route::get('/check-coverage', [\App\Http\Controllers\LocationController::class, 'checkCoverage'])->name('location.check-coverage');

    // API endpoints for farm data
    Route::prefix('api/farms')->group(function () {
        // Get all farms for the current user
        Route::get('/', function () {
            $farms = auth()->user()->farms;
            return response()->json($farms);
        });

        // Get NDVI data for a farm - Protected by subscription middleware
        Route::middleware(['subscription'])->get('/{farm}/ndvi', function (\App\Models\Farm $farm) {
            // Check if user has access to this farm
            if ($farm->user_id !== auth()->id()) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Get NDVI data from Google Earth Engine service
            $geeService = app(\App\Services\GoogleEarthEngineService::class);
            $ndviData = $geeService->getNDVIData($farm->latitude, $farm->longitude);

            // Format response
            return response()->json([
                'farm_id' => $farm->id,
                'ndvi_value' => $ndviData['current_ndvi'] ?? 0.5,
                'farm_center' => [
                    'lat' => $farm->latitude,
                    'lng' => $farm->longitude
                ],
                'farm_boundary' => json_decode($farm->boundary_coordinates) ?? [
                    ['lat' => $farm->latitude + 0.001, 'lng' => $farm->longitude + 0.001],
                    ['lat' => $farm->latitude + 0.001, 'lng' => $farm->longitude - 0.001],
                    ['lat' => $farm->latitude - 0.001, 'lng' => $farm->longitude - 0.001],
                    ['lat' => $farm->latitude - 0.001, 'lng' => $farm->longitude + 0.001]
                ]
            ]);
        });

        // Get rainfall data for a farm - Protected by subscription middleware
        Route::middleware(['subscription'])->get('/{farm}/rainfall', function (\App\Models\Farm $farm) {
            // Check if user has access to this farm
            if ($farm->user_id !== auth()->id()) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Get weather data from Weather service
            $weatherService = app(\App\Services\WeatherService::class);
            $weatherData = $weatherService->getWeatherData($farm->latitude, $farm->longitude);

            // Generate monthly rainfall data
            $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            $values = [];

            // Use forecast data if available, otherwise generate realistic data
            if (isset($weatherData['forecast']) && count($weatherData['forecast']) > 0) {
                foreach ($months as $month) {
                    $values[] = rand(10, 200); // Placeholder - would use actual forecast data
                }
            } else {
                // Generate realistic rainfall pattern for Nigeria
                // Rainy season is typically April to October
                for ($i = 0; $i < 12; $i++) {
                    if ($i >= 3 && $i <= 9) {
                        // Rainy season
                        $values[] = 100 + rand(0, 200);
                    } else {
                        // Dry season
                        $values[] = rand(0, 50);
                    }
                }
            }

            return response()->json([
                'labels' => $months,
                'values' => $values
            ]);
        });

        // Get drought alert for a farm - Protected by subscription middleware
        Route::middleware(['subscription'])->get('/{farm}/drought-alert', function (\App\Models\Farm $farm) {
            // Check if user has access to this farm
            if ($farm->user_id !== auth()->id()) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Get weather data from Weather service
            $weatherService = app(\App\Services\WeatherService::class);
            $weatherData = $weatherService->getWeatherData($farm->latitude, $farm->longitude);

            // Get NDVI data from Google Earth Engine service
            $geeService = app(\App\Services\GoogleEarthEngineService::class);
            $ndviData = $geeService->getNDVIData($farm->latitude, $farm->longitude);

            // Calculate drought risk
            $rainfall = $weatherData['annual_rainfall'] ?? 800;
            $ndvi = $ndviData['current_ndvi'] ?? 0.5;

            // Determine severity based on rainfall and NDVI
            $severity = 'low';
            $message = 'No drought alerts for your region at this time.';

            if ($rainfall < 500 && $ndvi < 0.3) {
                $severity = 'high';
                $message = 'FEWS NET Warning: High risk of drought in your region. Consider drought-resistant crops and water conservation measures.';
            } else if ($rainfall < 700 && $ndvi < 0.5) {
                $severity = 'medium';
                $message = 'FEWS NET Advisory: Moderate risk of drought conditions developing. Monitor water usage and consider supplemental irrigation.';
            }

            return response()->json([
                'severity' => $severity,
                'message' => $message,
                'source' => 'FEWS NET (Famine Early Warning Systems Network)',
                'last_updated' => now()->format('Y-m-d')
            ]);
        });
    });

    // Insurance Policies - Protected by subscription middleware
    Route::middleware(['subscription'])->group(function () {
        Route::resource('policies', PolicyController::class);

        // Parametric Insurance Calculator - must come before the {policy} routes to avoid conflicts
        Route::get('policies/parametric-calculator', [PolicyController::class, 'showParametricCalculator'])->name('policies.parametric-calculator');
        Route::get('parametric-calculator', [PolicyController::class, 'showParametricCalculator'])->name('parametric-calculator');
        Route::post('policies/calculate-parametric-premium', [PolicyController::class, 'calculateParametricPremium'])->name('calculate-parametric-premium');

        Route::get('policies/create/{farm_id?}', [PolicyController::class, 'create'])->name('policies.create.with-farm')->middleware('check.insurance.eligibility');
        Route::get('policies/{policy}/payment', [PolicyController::class, 'payment'])->name('policies.payment');
        Route::post('policies/{policy}/process-payment', [PolicyController::class, 'processPayment'])->name('policies.process-payment');
        Route::get('policies/{policy}/download', [PolicyController::class, 'download'])->name('policies.download');
        Route::get('policies/{policy}/renew', [PolicyController::class, 'renew'])->name('policies.renew')->middleware('check.insurance.eligibility');
    });

    // Claims - Protected by subscription middleware
    Route::middleware(['subscription'])->group(function () {
        Route::resource('claims', ClaimController::class);
    });

    // Wallet
    Route::prefix('wallet')->name('wallet.')->middleware(['auth'])->group(function () {
        Route::get('/', [WalletController::class, 'index'])->name('index');
        Route::get('/deposit', [WalletController::class, 'showDepositForm'])->name('deposit');
        Route::post('/deposit', [WalletController::class, 'processDeposit'])->name('process-deposit');
        Route::post('/deposit/momo', [WalletController::class, 'depositWithMomo'])->name('deposit.momo');
        Route::get('/withdraw', [WalletController::class, 'showWithdrawForm'])->name('withdraw');
        Route::post('/withdraw', [WalletController::class, 'processWithdraw'])->name('process-withdraw');
        Route::get('/transactions', [WalletController::class, 'transactions'])->name('transactions');
        Route::get('/transaction/{transaction}/download', [WalletController::class, 'downloadTransaction'])->name('transaction.download');

        // Virtual Accounts
        Route::get('/virtual-accounts', [\App\Http\Controllers\VirtualAccountController::class, 'index'])->name('virtual-accounts.index');
        Route::get('/virtual-accounts/create', [\App\Http\Controllers\VirtualAccountController::class, 'create'])->name('virtual-accounts.create');
        Route::post('/virtual-accounts', [\App\Http\Controllers\VirtualAccountController::class, 'store'])->name('virtual-accounts.store');
        Route::get('/virtual-accounts/{virtualAccount}', [\App\Http\Controllers\VirtualAccountController::class, 'show'])->name('virtual-accounts.show');
        Route::delete('/virtual-accounts/{virtualAccount}', [\App\Http\Controllers\VirtualAccountController::class, 'destroy'])->name('virtual-accounts.destroy');
        Route::post('/virtual-accounts/{virtualAccount}/requery', [\App\Http\Controllers\VirtualAccountController::class, 'requery'])->name('virtual-accounts.requery');

        // This route must be last as it's a catch-all
        Route::get('/{wallet}', [WalletController::class, 'show'])->name('show');
    });

    // Subscriptions
    Route::get('/subscriptions', [SubscriptionController::class, 'index'])->name('subscriptions.index');
    Route::get('/subscriptions/{plan}/subscribe', [SubscriptionController::class, 'subscribe'])->name('subscriptions.subscribe');
    Route::post('/subscriptions/{plan}/process-payment', [SubscriptionController::class, 'processPayment'])->name('subscriptions.process-payment');
    Route::get('/subscriptions/success/{subscription}', [SubscriptionController::class, 'success'])->name('subscriptions.success');
    Route::get('/subscriptions/show', [SubscriptionController::class, 'show'])->name('subscriptions.show');
    Route::post('/subscriptions/cancel', [SubscriptionController::class, 'cancel'])->name('subscriptions.cancel');

    // Legacy Business Module Subscription (for backward compatibility)
    Route::get('/subscriptions/{plan}/business-module', [SubscriptionController::class, 'businessModule'])->name('subscriptions.business-module');
    Route::post('/subscriptions/{plan}/process-business-module', [SubscriptionController::class, 'processBusinessModule'])->name('subscriptions.process-business-module');

    // Standalone Business Module Subscription
    Route::get('/business-module', [BusinessModuleSubscriptionController::class, 'index'])->name('business-module.index');
    Route::get('/business-module/subscribe', [BusinessModuleSubscriptionController::class, 'subscribe'])->name('business-module.subscribe');
    Route::post('/business-module/process-payment', [BusinessModuleSubscriptionController::class, 'processPayment'])->name('business-module.process-payment');
    Route::get('/business-module/success/{businessModuleSubscription}', [BusinessModuleSubscriptionController::class, 'success'])->name('business-module.success');
    Route::get('/business-module/show', [BusinessModuleSubscriptionController::class, 'show'])->name('business-module.show');
    Route::post('/business-module/cancel', [BusinessModuleSubscriptionController::class, 'cancel'])->name('business-module.cancel');

    // Business Module Setup Flow
    Route::prefix('business-module/setup')->name('business-module.setup.')->middleware(['auth'])->group(function () {
        Route::get('/', [\App\Http\Controllers\BusinessModuleSetupController::class, 'index'])->name('index');
        Route::get('/profile', [\App\Http\Controllers\BusinessModuleSetupController::class, 'showProfileSetup'])->name('profile');
        Route::post('/profile', [\App\Http\Controllers\BusinessModuleSetupController::class, 'storeProfileSetup'])->name('profile.store');
        Route::get('/bank-account', [\App\Http\Controllers\BusinessModuleSetupController::class, 'showBankAccountSetup'])->name('bank-account');
        Route::post('/bank-account', [\App\Http\Controllers\BusinessModuleSetupController::class, 'storeBankAccountSetup'])->name('bank-account.store');
        Route::get('/virtual-account', [\App\Http\Controllers\BusinessModuleSetupController::class, 'showVirtualAccountSetup'])->name('virtual-account');
        Route::post('/virtual-account', [\App\Http\Controllers\BusinessModuleSetupController::class, 'storeVirtualAccountSetup'])->name('virtual-account.store');
        Route::get('/complete', [\App\Http\Controllers\BusinessModuleSetupController::class, 'showSetupComplete'])->name('complete');
    });

    // Account/Profile
    Route::get('/profile', [AccountController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [AccountController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [AccountController::class, 'destroy'])->name('profile.destroy');

    // Account Settings
    Route::get('/account/settings', [AccountController::class, 'settings'])->middleware(\App\Http\Middleware\TwoFactorAuth::class)->name('account.settings');
    Route::patch('/account/settings/profile', [AccountController::class, 'updateProfile'])->name('account.settings.profile.update');
    Route::patch('/account/settings/currency', [AccountController::class, 'updateCurrency'])->name('account.settings.currency.update');
    Route::patch('/account/settings/notifications', [AccountController::class, 'updateNotifications'])->name('account.settings.notifications.update');
    Route::post('/account/settings/fund', [AccountController::class, 'fundWallet'])->name('account.settings.fund');
    Route::post('/account/settings/withdraw', [AccountController::class, 'withdrawFunds'])->name('account.settings.withdraw');
    Route::post('/account/settings/bvn', [AccountController::class, 'updateBvn'])->name('account.settings.bvn.update');

    // Notification Settings
    Route::get('/notifications/settings', [\App\Http\Controllers\NotificationSettingsController::class, 'index'])->name('notifications.settings');
    Route::put('/notifications/settings', [\App\Http\Controllers\NotificationSettingsController::class, 'update'])->name('notifications.update');
    Route::post('/notifications/test', [\App\Http\Controllers\NotificationSettingsController::class, 'testNotification'])->name('notifications.test');

    // KYC Verification
    Route::get('/account/kyc', [KYCController::class, 'index'])->name('account.kyc');
    Route::post('/account/kyc', [KYCController::class, 'store'])->name('account.kyc.store');

    // Password update
    Route::put('/password', [PasswordController::class, 'update'])->name('password.update');

    // Password confirmation
    Route::get('/confirm-password', function () {
        return view('auth.confirm-password');
    })->name('password.confirm');
    Route::post('/confirm-password', [\App\Http\Controllers\Auth\ConfirmablePasswordController::class, 'store']);

    // Support
    Route::get('/support', [SupportController::class, 'index'])->name('support.index');
    Route::post('/support', [SupportController::class, 'store'])->name('support.store');
    Route::get('/support/tickets', [SupportController::class, 'tickets'])->name('support.tickets');
    Route::get('/support/{ticket}', [SupportController::class, 'show'])->name('support.show');
    Route::post('/support/{ticket}/respond', [SupportController::class, 'respond'])->name('support.respond');
    Route::post('/support/{ticket}/close', [SupportController::class, 'close'])->name('support.close');
    Route::post('/support/{ticket}/reopen', [SupportController::class, 'reopen'])->name('support.reopen');

    // Bank Accounts
    Route::prefix('bank-accounts')->name('bank-accounts.')->group(function () {
        Route::get('/', [BankAccountController::class, 'index'])->name('index');
        Route::get('/create', [BankAccountController::class, 'create'])->name('create');
        Route::post('/', [BankAccountController::class, 'store'])->name('store');
        Route::get('/{bankAccount}/edit', [BankAccountController::class, 'edit'])->name('edit');
        Route::put('/{bankAccount}', [BankAccountController::class, 'update'])->name('update');
        Route::delete('/{bankAccount}', [BankAccountController::class, 'destroy'])->name('destroy');
    });

    // Additional Bank Account Routes
    Route::prefix('account/bank-accounts')->name('account.bank-accounts.')->group(function () {
        Route::post('/', [BankAccountController::class, 'store'])->name('store');
        Route::patch('/{bankAccount}/set-default', [BankAccountController::class, 'setDefault'])->name('set-default');
        Route::get('/{bankAccount}/edit', [BankAccountController::class, 'edit'])->name('edit');
        Route::patch('/{bankAccount}', [BankAccountController::class, 'update'])->name('update');
        Route::delete('/{bankAccount}', [BankAccountController::class, 'destroy'])->name('destroy');
    });

    // Email Verification
    Route::post('/email/verification-notification', function (Request $request) {
        $request->user()->sendEmailVerificationNotification();
        return back()->with('status', 'verification-link-sent');
    })->middleware('throttle:6,1')->name('verification.send');

    Route::get('/email/verify', function () {
        return view('auth.verify-email');
    })->name('verification.notice');

    // Paystack
    Route::get('/paystack/callback', [PaystackController::class, 'handleCallback'])->name('paystack.callback');
    Route::post('/paystack/webhook', [\App\Http\Controllers\PaystackWebhookController::class, 'handleWebhook'])->name('paystack.webhook');

    // WhatsApp Webhook (outside auth middleware for external access)
});

// WhatsApp webhook routes (public - no auth required)

// WhatsApp test routes (for development)
Route::get('/whatsapp/test', function() {
    $whatsappService = app(\App\Services\WhatsAppService::class);
    $testNumber = request('phone', '1234567890'); // Replace with your test number (without +)
    $testMessage = request('message', 'Hello from Ije AI! This is a test message from WhatsApp Business API.');

    $result = $whatsappService->sendMessage($testNumber, $testMessage);

    return response()->json([
        'success' => $result['success'],
        'message' => $result['message'],
        'data' => $result['data'] ?? null,
        'error' => $result['error'] ?? null,
    ]);
})->name('whatsapp.test');

Route::get('/whatsapp/config', function() {
    return response()->json([
        'whatsapp_enabled' => config('chatbot.whatsapp.enabled'),
        'allow_guests' => config('chatbot.whatsapp.allow_guests'),
        'whatsapp_configured' => !empty(config('services.whatsapp.access_token')) && !empty(config('services.whatsapp.phone_number_id')),
        'webhook_url' => route('whatsapp.webhook'),
        'verify_url' => route('whatsapp.webhook.verify'),
        'api_version' => config('services.whatsapp.version'),
    ]);
})->name('whatsapp.config');

Route::middleware(['auth', 'verified'])->group(function () {

    // Logout
    Route::post('/logout', [AuthenticatedSessionController::class, 'destroy'])->name('logout');

    // Soil Sensor Routes (Coming Soon)
    Route::get('/soil-sensors/dashboard/{farmId}', [SoilSensorController::class, 'dashboard'])->name('soil-sensors.dashboard')->middleware('coming.soon');
    Route::get('/soil-sensors/configure/{farmId}', [SoilSensorController::class, 'configure'])->name('soil-sensors.configure')->middleware('coming.soon');
    Route::get('/soil-sensors/alerts/{farmId}', [SoilSensorAlertController::class, 'index'])->name('soil-sensors.alerts')->middleware('coming.soon');
});


// Email verification routes
Route::middleware(['auth'])->group(function () {
    Route::get('/email/verify', function () {
        return view('auth.verify-email');
    })->name('verification.notice');

    Route::post('/email/verification-notification', function (Request $request) {
        $request->user()->sendEmailVerificationNotification();
        return back()->with('status', 'verification-link-sent');
    })->middleware('throttle:6,1')->name('verification.send');

    // Email verification success page
    Route::get('/email/verified', function () {
        return view('auth.verified-email');
    })->name('verification.success');
});
// Email verification route that requires signed middleware
Route::get('/email/verify/{id}/{hash}', function (EmailVerificationRequest $request) {
    $request->fulfill();
    return redirect()->route('verification.success');
})->middleware(['auth', 'signed'])->name('verification.verify');

// API Proxy Routes
Route::middleware('auth')->group(function () {
    Route::get('/api/proxy/weather', [ApiProxyController::class, 'getWeather']);
    Route::get('/api/proxy/climate', [ApiProxyController::class, 'getClimate']);
    Route::get('/api/proxy/soil', [ApiProxyController::class, 'getSoil']);
    Route::get('/api/proxy/esri-soil', [ApiProxyController::class, 'getEsriSoil']);
});

// Loan Calculator and Application Routes
Route::middleware(['auth'])->group(function () {
    // RCP Score Routes - Only accessible from loans index
    Route::get('/loans/rcp-score', [App\Http\Controllers\RCPController::class, 'show'])
        ->middleware('check.rcp.access')
        ->name('loans.rcp-score');

    // Loan Offers Routes
    Route::get('/loans/offers', [App\Http\Controllers\LoanCalculatorController::class, 'index'])->name('loans.offers');
    Route::post('/loans/offers/calculate', [App\Http\Controllers\LoanCalculatorController::class, 'calculate'])->name('loans.calculator.calculate');

    // Redirect old calculator URL to new offers URL
    Route::get('/loans/calculator', function() {
        return redirect()->route('loans.offers');
    });

    // Loan Application Routes
    Route::get('/loans/applications', [App\Http\Controllers\LoanApplicationController::class, 'index'])->name('loans.applications.index');
    Route::get('/loans/applications/create/{bankLoanOffer}', [App\Http\Controllers\LoanApplicationController::class, 'create'])->name('loans.applications.create')->middleware(['check.loan.eligibility', 'check.location:loan']);
    Route::post('/loans/applications/{bankLoanOffer}', [App\Http\Controllers\LoanApplicationController::class, 'store'])->name('loans.applications.store')->middleware(['check.loan.eligibility', 'check.location:loan']);
    Route::get('/loans/applications/{loanApplication}', [App\Http\Controllers\LoanApplicationController::class, 'show'])->name('loans.applications.show');
    Route::post('/loans/applications/{loanApplication}/cancel', [App\Http\Controllers\LoanApplicationController::class, 'cancel'])->name('loans.applications.cancel');

    // Existing Loan Routes
    Route::get('/loans', [App\Http\Controllers\LoanController::class, 'index'])->name('loans.index');

    // Redirect /loans/create to /loans
    Route::get('/loans/create', function() {
        return redirect()->route('loans.index');
    })->name('loans.create');

    Route::post('/loans', [App\Http\Controllers\LoansController::class, 'store'])
        ->middleware(['check.loan.eligibility', 'check.location:loan'])
        ->name('loans.store');
    Route::get('/loans/{loan}', [App\Http\Controllers\LoansController::class, 'show'])->name('loans.show');
    Route::post('/loans/{loan}/repay', [App\Http\Controllers\LoansController::class, 'repay'])->name('loans.repay');
});

// Bank admin routes
Route::middleware(['auth', 'role:bank', 'bank'])->prefix('bank')->name('bank.')->group(function () {
    // Dashboard
    Route::get('/', [\App\Http\Controllers\Bank\DashboardController::class, 'index'])->name('dashboard');
    Route::get('/profile', [\App\Http\Controllers\Bank\DashboardController::class, 'profile'])->name('profile');
    Route::get('/profile/edit', [\App\Http\Controllers\Bank\ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [\App\Http\Controllers\Bank\ProfileController::class, 'update'])->name('profile.update');
    Route::get('/profile/terms/edit', [\App\Http\Controllers\Bank\ProfileController::class, 'editTerms'])->name('profile.edit-terms');
    Route::put('/profile/terms', [\App\Http\Controllers\Bank\ProfileController::class, 'updateTerms'])->name('profile.update-terms');
    Route::get('/analytics', [\App\Http\Controllers\Bank\DashboardController::class, 'analytics'])->name('analytics');
    Route::get('/api', [\App\Http\Controllers\Bank\DashboardController::class, 'api'])->name('api');

    // Customers
    Route::get('/customers', [\App\Http\Controllers\Bank\DashboardController::class, 'customers'])->name('customers.index');
    Route::get('/customers/{customer}', [\App\Http\Controllers\Bank\DashboardController::class, 'showCustomer'])->name('customers.show');
    Route::get('/customers/{customer}/farm-activities', [\App\Http\Controllers\Bank\DashboardController::class, 'farmActivities'])->name('customers.farm-activities');
    // Bank Loan Application Routes
    Route::get('/loan-applications', [App\Http\Controllers\Bank\LoanApplicationController::class, 'index'])->name('loan-applications.index');
    Route::get('/loan-applications/{loanApplication}', [App\Http\Controllers\Bank\LoanApplicationController::class, 'show'])->name('loan-applications.show');
    Route::get('/loan-applications/{loanApplication}/monitor', [App\Http\Controllers\Bank\LoanApplicationController::class, 'monitor'])->name('loan-applications.monitor');
    Route::post('/loan-applications/{loanApplication}/approve', [App\Http\Controllers\Bank\LoanApplicationController::class, 'approve'])->name('loan-applications.approve');
    Route::post('/loan-applications/{loanApplication}/reject', [App\Http\Controllers\Bank\LoanApplicationController::class, 'reject'])->name('loan-applications.reject');
    Route::post('/loan-applications/{loanApplication}/disburse', [App\Http\Controllers\Bank\LoanApplicationController::class, 'disburse'])->name('loan-applications.disburse');

    // Existing Bank Loan Routes
    Route::get('/loans', [App\Http\Controllers\Bank\LoanController::class, 'index'])->name('loans.index');
    Route::get('/loans/settings', [App\Http\Controllers\Bank\LoanController::class, 'settings'])->name('loans.settings');
    Route::post('/loans/settings', [App\Http\Controllers\Bank\LoanController::class, 'updateSettings'])->name('loans.settings.update');
    Route::get('/loans/{loan}', [App\Http\Controllers\Bank\LoanController::class, 'show'])->name('loans.show');
    Route::post('/loans/{loan}/approve', [App\Http\Controllers\Bank\LoanController::class, 'approve'])->name('loans.approve');
    Route::post('/loans/{loan}/reject', [App\Http\Controllers\Bank\LoanController::class, 'reject'])->name('loans.reject');

    // Bank Loan Offer Routes
    Route::get('/loan-offers', [App\Http\Controllers\Bank\LoanOfferController::class, 'index'])->name('loan-offers.index');
    Route::get('/loan-offers/create', [App\Http\Controllers\Bank\LoanOfferController::class, 'create'])->name('loan-offers.create');
    Route::post('/loan-offers', [App\Http\Controllers\Bank\LoanOfferController::class, 'store'])->name('loan-offers.store');
    Route::get('/loan-offers/{bankLoanOffer}', [App\Http\Controllers\Bank\LoanOfferController::class, 'show'])->name('loan-offers.show');
    Route::get('/loan-offers/{bankLoanOffer}/edit', [App\Http\Controllers\Bank\LoanOfferController::class, 'edit'])->name('loan-offers.edit');
    Route::put('/loan-offers/{bankLoanOffer}', [App\Http\Controllers\Bank\LoanOfferController::class, 'update'])->name('loan-offers.update');
    Route::delete('/loan-offers/{bankLoanOffer}', [App\Http\Controllers\Bank\LoanOfferController::class, 'destroy'])->name('loan-offers.destroy');
});

// Add these routes to your web.php file
Route::middleware(['auth'])->group(function () {
    // Transaction routes
    Route::get('/transactions/export', [App\Http\Controllers\TransactionController::class, 'export'])->name('transactions.export');

    // You can add a placeholder route for referrals if you want to implement it later
    // Route::get('/referrals', [App\Http\Controllers\ReferralController::class, 'index'])->name('referrals.index');
});

// Two-factor authentication routes
Route::middleware(['auth'])->group(function () {
    Route::get('two-factor/setup', [TwoFactorAuthController::class, 'setup'])
        ->name('two-factor.setup');

    Route::post('two-factor/enable', [TwoFactorAuthController::class, 'enable'])
        ->name('two-factor.enable');

    Route::post('two-factor/disable', [TwoFactorAuthController::class, 'disable'])
        ->name('two-factor.disable');

    Route::get('two-factor/confirm', [TwoFactorAuthController::class, 'confirmForm'])
        ->name('two-factor.confirm');

    Route::post('two-factor/confirm', [TwoFactorAuthController::class, 'confirm']);

    Route::get('two-factor/recovery-codes', [TwoFactorAuthController::class, 'showRecoveryCodes'])
        ->name('two-factor.recovery-codes');
});

// Test route for PDF settings
Route::get('/test-pdf-settings', function() {
    $headerLogo = setting('pdf_header_logo');
    $signatureImage = setting('pdf_signature_image');

    $headerLogoPath = $headerLogo ? pdf_asset($headerLogo) : null;
    $signatureImagePath = $signatureImage ? pdf_asset($signatureImage) : null;

    $headerLogoData = $headerLogo ? pdf_image_data_url($headerLogo) : null;
    $signatureImageData = $signatureImage ? pdf_image_data_url($signatureImage) : null;

    return [
        'headerLogo' => $headerLogo,
        'signatureImage' => $signatureImage,
        'headerLogoPath' => $headerLogoPath,
        'signatureImagePath' => $signatureImagePath,
        'headerLogoExists' => $headerLogoPath ? file_exists($headerLogoPath) : false,
        'signatureImageExists' => $signatureImagePath ? file_exists($signatureImagePath) : false,
        'headerLogoDataUrl' => $headerLogoData ? 'Data URL generated (length: ' . strlen($headerLogoData) . ')' : null,
        'signatureImageDataUrl' => $signatureImageData ? 'Data URL generated (length: ' . strlen($signatureImageData) . ')' : null,
    ];
});

// Test route for PDF generation
Route::get('/test-pdf', function() {
    $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadView('layouts.test-pdf', [
        'title' => 'Test PDF Document'
    ])->setOptions([
        'enable_remote' => true,
        'enable_php' => true,
        'enable_javascript' => true,
        'isRemoteEnabled' => true,
        'isPhpEnabled' => true,
        'isHtml5ParserEnabled' => true,
        'chroot' => public_path(),
        'allowed_protocols' => [
            'file://' => ['rules' => []],
            'http://' => ['rules' => []],
            'https://' => ['rules' => []],
            'data://' => ['rules' => []],
        ],
        'font_dir' => storage_path('fonts/'),
        'font_cache' => storage_path('fonts/'),
    ]);

    return $pdf->download('test.pdf');
});

// Test route for user export
Route::get('/test-user-export', [\App\Http\Controllers\Admin\UserController::class, 'export']);

// Documentation routes (public) - moved to developers.php to avoid conflicts
// Route::get('/docs', [\App\Http\Controllers\DeveloperController::class, 'docs'])->name('docs');
// Route::get('/docs/category/{category}', [\App\Http\Controllers\DeveloperController::class, 'category'])->name('docs.category');

// Test route for developers docs
Route::get('/developers/docs/test', function() {
    return 'Developers docs test route is working!';
});

// Direct routes for developers docs
Route::get('/developers/docs', function() {
    return app()->make(\App\Http\Controllers\Developers\DocumentationController::class)->index();
})->name('developers.docs.index');

Route::get('/developers/docs/{category}', function($category) {
    return app()->make(\App\Http\Controllers\Developers\DocumentationController::class)->category($category);
})->name('developers.docs.category');

Route::get('/developers/docs/{category}/{endpoint}', function($category, $endpoint) {
    return app()->make(\App\Http\Controllers\Developers\DocumentationController::class)->endpoint($category, $endpoint);
})->name('developers.docs.endpoint');

// Developer Portal Routes
Route::prefix('developers')->name('developers.')->group(function () {
    // Public routes
    Route::get('/', [\App\Http\Controllers\Developers\HomeController::class, 'index'])->name('home');

    // Authentication routes
    Route::get('/login', [\App\Http\Controllers\Developers\AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [\App\Http\Controllers\Developers\AuthController::class, 'login']);
    Route::get('/register', [\App\Http\Controllers\Developers\AuthController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [\App\Http\Controllers\Developers\AuthController::class, 'register']);

    // Social login routes
    Route::get('/login/github', [\App\Http\Controllers\Developers\AuthController::class, 'redirectToGithub'])->name('login.github');
    Route::get('/login/github/callback', [\App\Http\Controllers\Developers\AuthController::class, 'handleGithubCallback']);
    Route::get('/login/google', [\App\Http\Controllers\Developers\AuthController::class, 'redirectToGoogle'])->name('login.google');
    Route::get('/login/google/callback', [\App\Http\Controllers\Developers\AuthController::class, 'handleGoogleCallback']);
    Route::get('/login/twitter', [\App\Http\Controllers\Developers\AuthController::class, 'redirectToTwitter'])->name('login.twitter');
    Route::get('/login/twitter/callback', [\App\Http\Controllers\Developers\AuthController::class, 'handleTwitterCallback']);

    // REMOVED: Duplicate developer password reset routes (handled in developers.php)

    // Documentation routes (public)
    Route::get('/docs/authentication', [\App\Http\Controllers\Developers\DocsController::class, 'authentication'])->name('docs.authentication');
    Route::get('/docs/errors', [\App\Http\Controllers\Developers\DocsController::class, 'errors'])->name('docs.errors');
    Route::get('/docs/pagination', [\App\Http\Controllers\Developers\DocsController::class, 'pagination'])->name('docs.pagination');
    Route::get('/docs/user', [\App\Http\Controllers\Developers\DocsController::class, 'user'])->name('docs.user');
    Route::get('/docs/farms', [\App\Http\Controllers\Developers\DocsController::class, 'farms'])->name('docs.farms');
    Route::get('/docs/weather', [\App\Http\Controllers\Developers\DocsController::class, 'weather'])->name('docs.weather');
    Route::get('/docs/insurance', [\App\Http\Controllers\Developers\DocsController::class, 'insurance'])->name('docs.insurance');
    Route::get('/docs/digital-card', [\App\Http\Controllers\Developers\DocsController::class, 'digitalCard'])->name('docs.digital-card');

    // Changelog (public)
    Route::get('/changelog', [\App\Http\Controllers\Developers\ChangelogController::class, 'index'])->name('changelog.index');

    // Authenticated routes
    Route::middleware(['auth', 'role:developer'])->group(function () {
        // Dashboard
        Route::get('/dashboard', [\App\Http\Controllers\Developers\DashboardController::class, 'index'])->name('dashboard');

        // API Keys
        Route::get('/api-keys', [\App\Http\Controllers\Developers\ApiKeyController::class, 'index'])->name('api-keys.index');
        Route::get('/api-keys/create', [\App\Http\Controllers\Developers\ApiKeyController::class, 'create'])->name('api-keys.create');
        Route::post('/api-keys', [\App\Http\Controllers\Developers\ApiKeyController::class, 'store'])->name('api-keys.store');
        Route::get('/api-keys/{apiKey}', [\App\Http\Controllers\Developers\ApiKeyController::class, 'show'])->name('api-keys.show');
        Route::post('/api-keys/{apiKey}/activate', [\App\Http\Controllers\Developers\ApiKeyController::class, 'activate'])->name('api-keys.activate');
        Route::post('/api-keys/{apiKey}/deactivate', [\App\Http\Controllers\Developers\ApiKeyController::class, 'deactivate'])->name('api-keys.deactivate');
        Route::post('/api-keys/{apiKey}/topup', [\App\Http\Controllers\Developers\ApiKeyController::class, 'topup'])->name('api-keys.topup');
        Route::delete('/api-keys/{apiKey}', [\App\Http\Controllers\Developers\ApiKeyController::class, 'destroy'])->name('api-keys.destroy');

        // API Usage
        Route::get('/api-usage', [\App\Http\Controllers\Developers\ApiUsageController::class, 'index'])->name('api-usage');
        Route::get('/api-usage/export', [\App\Http\Controllers\Developers\ApiUsageController::class, 'export'])->name('api-usage.export');

        // Wallet
        Route::get('/wallet', [\App\Http\Controllers\Developers\WalletController::class, 'index'])->name('wallet');
        Route::get('/wallet/topup', [\App\Http\Controllers\Developers\WalletController::class, 'topup'])->name('wallet.topup');
        Route::post('/wallet/topup', [\App\Http\Controllers\Developers\WalletController::class, 'processTopup'])->name('wallet.process-topup');
        Route::get('/wallet/transactions', [\App\Http\Controllers\Developers\WalletController::class, 'transactions'])->name('wallet.transactions');

        // REMOVED: Duplicate developer support routes (handled in developers.php)

        // Profile
        Route::get('/profile', [\App\Http\Controllers\Developers\ProfileController::class, 'index'])->name('profile');
        Route::put('/profile', [\App\Http\Controllers\Developers\ProfileController::class, 'update'])->name('profile.update');

        // Settings
        Route::get('/settings', [\App\Http\Controllers\Developers\SettingsController::class, 'index'])->name('settings');
        Route::put('/settings', [\App\Http\Controllers\Developers\SettingsController::class, 'update'])->name('settings.update');
    });
});

// REMOVED: Duplicate Admin Developer Portal Routes (handled in admin-developers.php)
// These routes were conflicting with the routes defined in admin-developers.php

// Bank Partner Auth Routes
Route::prefix('bank')->name('bank.')->group(function () {
    Route::middleware('guest')->group(function () {
        Route::get('/login', [\App\Http\Controllers\Bank\BankLoginController::class, 'showLoginForm'])->name('login');
        Route::post('/login', [\App\Http\Controllers\Bank\BankLoginController::class, 'login'])->name('login.submit');

        // 2FA Login Verification (guest routes since user is temporarily logged out)
        Route::get('/2fa/login-verify', [\App\Http\Controllers\Bank\TwoFactorController::class, 'showLoginVerification'])->name('2fa.login-verify');
        Route::post('/2fa/login-verify', [\App\Http\Controllers\Bank\TwoFactorController::class, 'verifyLogin'])->name('2fa.login-verify.submit');
    });

    Route::middleware(['auth', 'role:bank', \App\Http\Middleware\BankSecurityMiddleware::class])->group(function () {
        Route::post('/logout', [\App\Http\Controllers\Bank\BankLoginController::class, 'logout'])->name('logout');

        // Dashboard
        Route::get('/dashboard', [\App\Http\Controllers\Bank\DashboardController::class, 'index'])->name('dashboard');

        // Loan Applications
        Route::get('/loan-applications', [\App\Http\Controllers\Bank\LoanApplicationController::class, 'index'])->name('loan-applications.index');
        Route::get('/loan-applications/{loanApplication}', [\App\Http\Controllers\Bank\LoanApplicationController::class, 'show'])->name('loan-applications.show');
        Route::post('/loan-applications/{loanApplication}/approve', [\App\Http\Controllers\Bank\LoanApplicationController::class, 'approve'])->name('loan-applications.approve');
        Route::post('/loan-applications/{loanApplication}/reject', [\App\Http\Controllers\Bank\LoanApplicationController::class, 'reject'])->name('loan-applications.reject');
        Route::post('/loan-applications/{loanApplication}/disburse', [\App\Http\Controllers\Bank\LoanApplicationController::class, 'disburse'])->name('loan-applications.disburse');
        Route::get('/loan-applications/{loanApplication}/monitor', [\App\Http\Controllers\Bank\LoanApplicationController::class, 'monitor'])->name('loan-applications.monitor');

        // Wallet Management
        Route::prefix('wallet')->name('wallet.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Bank\WalletController::class, 'index'])->name('index');
            Route::get('/transactions', [\App\Http\Controllers\Bank\WalletController::class, 'transactions'])->name('transactions');
            Route::get('/transactions/export', [\App\Http\Controllers\Bank\WalletController::class, 'exportTransactions'])->name('transactions.export');

            // Bank Accounts
            Route::get('/accounts', [\App\Http\Controllers\Bank\WalletController::class, 'accounts'])->name('accounts');
            Route::post('/accounts', [\App\Http\Controllers\Bank\WalletController::class, 'storeAccount'])->name('accounts.store');
            Route::put('/accounts/{account}', [\App\Http\Controllers\Bank\WalletController::class, 'updateAccount'])->name('accounts.update');
            Route::post('/accounts/{account}/set-primary', [\App\Http\Controllers\Bank\WalletController::class, 'setPrimaryAccount'])->name('accounts.set-primary');
            Route::delete('/accounts/{account}', [\App\Http\Controllers\Bank\WalletController::class, 'deleteAccount'])->name('accounts.delete');

            // Virtual Accounts - exact same pattern as users
            Route::get('/virtual-accounts', [\App\Http\Controllers\Bank\WalletController::class, 'virtualAccounts'])->name('virtual-accounts');
            Route::get('/virtual-accounts/create', [\App\Http\Controllers\Bank\WalletController::class, 'createVirtualAccount'])->name('virtual-accounts.create');
            Route::post('/virtual-accounts', [\App\Http\Controllers\Bank\WalletController::class, 'storeVirtualAccount'])->name('virtual-accounts.store');
            Route::get('/virtual-accounts/{virtualAccount}', [\App\Http\Controllers\Bank\WalletController::class, 'showVirtualAccount'])->name('virtual-accounts.show');
        });

        // Security Management
        Route::prefix('security')->name('security.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Bank\SecurityController::class, 'index'])->name('index');
            Route::get('/login-history', [\App\Http\Controllers\Bank\SecurityController::class, 'loginHistory'])->name('login-history');
            Route::get('/alerts', [\App\Http\Controllers\Bank\SecurityController::class, 'alerts'])->name('alerts');
            Route::post('/alerts/{alert}/mark-read', [\App\Http\Controllers\Bank\SecurityController::class, 'markAlertAsRead'])->name('alerts.mark-read');
            Route::post('/alerts/mark-all-read', [\App\Http\Controllers\Bank\SecurityController::class, 'markAllAlertsAsRead'])->name('alerts.mark-all-read');
            Route::post('/alerts/settings', [\App\Http\Controllers\Bank\SecurityController::class, 'updateAlertSettings'])->name('alerts.settings');
            Route::post('/sessions/{session}/terminate', [\App\Http\Controllers\Bank\SecurityController::class, 'terminateSession'])->name('terminate-session');
            Route::post('/sessions/terminate-all', [\App\Http\Controllers\Bank\SecurityController::class, 'terminateAllSessions'])->name('terminate-all-sessions');
            Route::put('/settings', [\App\Http\Controllers\Bank\SecurityController::class, 'updateSettings'])->name('settings.update');
            Route::get('/kyc', [\App\Http\Controllers\Bank\SecurityController::class, 'kyc'])->name('kyc');
        });

        // 2FA Management
        Route::prefix('2fa')->name('2fa.')->group(function () {
            Route::get('/setup', [\App\Http\Controllers\Bank\TwoFactorController::class, 'setup'])->name('setup');
            Route::post('/enable', [\App\Http\Controllers\Bank\TwoFactorController::class, 'enable'])->name('enable');
            Route::post('/disable', [\App\Http\Controllers\Bank\TwoFactorController::class, 'disable'])->name('disable');
            Route::get('/verify', [\App\Http\Controllers\Bank\TwoFactorController::class, 'showVerification'])->name('verify');
            Route::post('/verify', [\App\Http\Controllers\Bank\TwoFactorController::class, 'verify'])->name('verify.submit');
            Route::get('/recovery-codes', [\App\Http\Controllers\Bank\TwoFactorController::class, 'recoveryCodes'])->name('recovery-codes');
            Route::post('/recovery-codes/regenerate', [\App\Http\Controllers\Bank\TwoFactorController::class, 'regenerateRecoveryCodes'])->name('recovery-codes.regenerate');
        });

        // Notifications
        Route::prefix('notifications')->name('notifications.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Bank\NotificationController::class, 'index'])->name('index');
            Route::post('/{notification}/read', [\App\Http\Controllers\Bank\NotificationController::class, 'markAsRead'])->name('read');
            Route::post('/mark-all-read', [\App\Http\Controllers\Bank\NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
        });

        // Reports
        Route::prefix('reports')->name('reports.')->group(function () {
            Route::get('/loans', [\App\Http\Controllers\Bank\ReportController::class, 'loans'])->name('loans');
            Route::get('/financial', [\App\Http\Controllers\Bank\ReportController::class, 'financial'])->name('financial');
            Route::get('/customers', [\App\Http\Controllers\Bank\ReportController::class, 'customers'])->name('customers');
            Route::get('/export/loans', [\App\Http\Controllers\Bank\ReportController::class, 'exportLoans'])->name('export.loans');
            Route::get('/export/financial', [\App\Http\Controllers\Bank\ReportController::class, 'exportFinancial'])->name('export.financial');
            Route::get('/export/customers', [\App\Http\Controllers\Bank\ReportController::class, 'exportCustomers'])->name('export.customers');
        });

        // Help & Support
        Route::prefix('help')->name('help.')->group(function () {
            Route::get('/documentation', [\App\Http\Controllers\Bank\HelpController::class, 'documentation'])->name('documentation');
            Route::get('/faq', [\App\Http\Controllers\Bank\HelpController::class, 'faq'])->name('faq');
        });

        Route::prefix('support')->name('support.')->group(function () {
            Route::get('/tickets', [\App\Http\Controllers\Bank\SupportController::class, 'tickets'])->name('tickets');
            Route::get('/contact', [\App\Http\Controllers\Bank\SupportController::class, 'contact'])->name('contact');
            Route::post('/tickets', [\App\Http\Controllers\Bank\SupportController::class, 'createTicket'])->name('tickets.create');
        });
    });
});

// Insurance Company Auth Routes
Route::prefix('insurance')->name('insurance.')->group(function () {
    Route::middleware('guest')->group(function () {
        Route::get('/login', [\App\Http\Controllers\Insurance\InsuranceLoginController::class, 'showLoginForm'])->name('login.form');
        Route::post('/login', [\App\Http\Controllers\Insurance\InsuranceLoginController::class, 'login'])->name('login');
    });

    Route::middleware(['auth', 'role:insurer'])->group(function () {
        Route::post('/logout', [\App\Http\Controllers\Insurance\InsuranceLoginController::class, 'logout'])->name('logout');
    });
});

// Insurance Company Routes
Route::prefix('insurance')->middleware(['auth', 'role:insurer', 'insurer'])->name('insurance.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [\App\Http\Controllers\Insurance\InsuranceController::class, 'dashboard'])->name('dashboard');

    // Insurance Products
    Route::get('/products', [\App\Http\Controllers\Insurance\InsuranceController::class, 'products'])->name('products.index');
    Route::get('/products/create', [\App\Http\Controllers\Insurance\InsuranceController::class, 'createProduct'])->name('products.create');
    Route::post('/products', [\App\Http\Controllers\Insurance\InsuranceController::class, 'storeProduct'])->name('products.store');
    Route::get('/products/{product}', [\App\Http\Controllers\Insurance\InsuranceController::class, 'showProduct'])->name('products.show');
    Route::get('/products/{product}/edit', [\App\Http\Controllers\Insurance\InsuranceController::class, 'editProduct'])->name('products.edit');
    Route::put('/products/{product}', [\App\Http\Controllers\Insurance\InsuranceController::class, 'updateProduct'])->name('products.update');

    // Users Management
    Route::get('/users', [\App\Http\Controllers\Insurance\InsuranceController::class, 'users'])->name('users.index');
    Route::get('/users/{user}', [\App\Http\Controllers\Insurance\InsuranceController::class, 'showUser'])->name('users.show');
    Route::get('/users/{user}/credit-wallet', [\App\Http\Controllers\Insurance\InsuranceController::class, 'showCreditWalletForm'])->name('users.credit-wallet.form');
    Route::post('/users/{user}/credit-wallet', [\App\Http\Controllers\Insurance\InsuranceController::class, 'creditWallet'])->name('users.credit-wallet');

    // Farms Management
    Route::get('/farms', [\App\Http\Controllers\Insurance\InsuranceController::class, 'farms'])->name('farms.index');
    Route::get('/farms/{farm}', [\App\Http\Controllers\Insurance\InsuranceController::class, 'showFarm'])->name('farms.show');
    Route::get('/farms/{farm}/weather', [\App\Http\Controllers\Insurance\InsuranceController::class, 'farmWeather'])->name('farms.weather');
    Route::get('/farms/{farm}/insights', [\App\Http\Controllers\Insurance\InsuranceController::class, 'farmInsights'])->name('farms.insights');

    // Claims Management
    Route::get('/claims', [\App\Http\Controllers\Insurance\InsuranceController::class, 'claims'])->name('claims.index');
    Route::get('/claims/{claim}', [\App\Http\Controllers\Insurance\InsuranceController::class, 'showClaim'])->name('claims.show');
    Route::post('/claims/{claim}/process', [\App\Http\Controllers\Insurance\InsuranceController::class, 'processClaim'])->name('claims.process');

    // Parametric Claims Management
    Route::get('/parametric-claims', [\App\Http\Controllers\Insurance\InsuranceController::class, 'parametricClaims'])->name('parametric-claims.index');
    Route::get('/parametric-claims/{claim}', [\App\Http\Controllers\Insurance\InsuranceController::class, 'showParametricClaim'])->name('parametric-claims.show');
    Route::post('/parametric-claims/{claim}/process', [\App\Http\Controllers\Insurance\InsuranceController::class, 'processParametricClaim'])->name('parametric-claims.process');

    // Account Settings
    Route::get('/settings', [\App\Http\Controllers\Insurance\InsuranceController::class, 'settings'])->name('settings');
    Route::put('/settings', [\App\Http\Controllers\Insurance\InsuranceController::class, 'updateSettings'])->name('settings.update');
});

// Fallback route for POST-only routes
Route::get('bank/loan-applications/{id}/{action}', function($id, $action) {
    if (in_array($action, ['approve', 'reject', 'disburse'])) {
        return redirect("bank/loan-applications/{$id}")
            ->with('error', "This action requires a POST request. Please use the buttons on the loan application page.");
    }

    abort(404);
})->where('id', '[0-9]+')->where('action', '(approve|reject|disburse)');

// Test route for Paystack configuration
Route::get('/test-paystack-config', function () {
    $config = [
        'baseUrl' => config('payment.paystack.payment_url'),
        'secretKeyExists' => !empty(config('payment.paystack.secret_key')),
        'publicKeyExists' => !empty(config('payment.paystack.public_key')),
    ];

    return response()->json($config);
});

// Add track action route for weather alerts
Route::middleware(['auth', 'subscription'])->post('/weather-alerts/{alert}/track-action', [\App\Http\Controllers\WeatherAlertController::class, 'trackActionClick'])->name('weather-alerts.track-action');

// Test weather alerts functionality (admin only)
Route::middleware(['auth', 'role:admin'])->group(function () {
    Route::get('/test-weather-alerts', [\App\Http\Controllers\WeatherAlertController::class, 'testWeatherAlerts'])->name('test.weather-alerts');
    Route::post('/trigger-weather-alerts', [\App\Http\Controllers\WeatherAlertController::class, 'triggerWeatherAlerts'])->name('trigger.weather-alerts');
});

// Test route for user activity log functionality
Route::get('/test-user-activity-log', function() {
    $user = \App\Models\User::first();
    if (!$user) {
        return response()->json(['error' => 'No users found']);
    }

    // Test creating an activity log
    \App\Models\UserActivityLog::logActivity(
        $user->id,
        'test_activity',
        'This is a test activity log entry',
        [
            'test_data' => 'sample metadata',
            'timestamp' => now()->toISOString()
        ]
    );

    // Get recent activity logs
    $activities = \App\Models\UserActivityLog::where('user_id', $user->id)
        ->orderBy('created_at', 'desc')
        ->take(5)
        ->get();

    return response()->json([
        'success' => true,
        'user' => $user->name,
        'recent_activities' => $activities->map(function($activity) {
            return [
                'type' => $activity->activity_type,
                'description' => $activity->description,
                'created_at' => $activity->created_at->format('Y-m-d H:i:s'),
                'metadata' => $activity->metadata
            ];
        })
    ]);
});

// Test route to generate recent activity logs
Route::get('/generate-test-activities', function() {
    $user = \App\Models\User::first();
    if (!$user) {
        return response()->json(['error' => 'No users found']);
    }

    // Generate some recent activities
    $activities = [
        [
            'type' => 'login',
            'description' => 'User logged in successfully',
            'metadata' => ['login_method' => 'email_password', 'remember_me' => false]
        ],
        [
            'type' => 'profile_update',
            'description' => 'User updated their phone number',
            'metadata' => ['changed_fields' => ['phone_number'], 'changes' => ['phone_number' => '+234' . rand(7000000000, 9999999999)]]
        ],
        [
            'type' => 'wallet_credit',
            'description' => 'Wallet credited via Paystack payment',
            'metadata' => ['amount' => rand(1000, 10000), 'payment_method' => 'paystack', 'transaction_reference' => 'TXN_' . strtoupper(\Illuminate\Support\Str::random(10))]
        ],
        [
            'type' => 'farm_created',
            'description' => 'User created a new farm: Test Farm ' . rand(1, 100),
            'metadata' => ['farm_name' => 'Test Farm ' . rand(1, 100), 'area_hectares' => rand(1, 10), 'crop_type' => ['Rice', 'Maize', 'Cassava'][rand(0, 2)]]
        ]
    ];

    foreach ($activities as $activity) {
        \App\Models\UserActivityLog::logActivity(
            $user->id,
            $activity['type'],
            $activity['description'],
            $activity['metadata']
        );
    }

    return response()->json([
        'success' => true,
        'message' => 'Generated ' . count($activities) . ' test activities for user: ' . $user->name,
        'user_id' => $user->id
    ]);
});

// Test admin user activity routes
Route::middleware(['auth', 'role:admin'])->prefix('admin/users')->name('admin.users.')->group(function () {
    Route::get('/{user}/activity-log', [\App\Http\Controllers\Admin\UserController::class, 'activityLog'])->name('activity-log');
    Route::post('/{user}/reset-password', [\App\Http\Controllers\Admin\UserController::class, 'resetPassword'])->name('reset-password');
    Route::post('/{user}/suspend', [\App\Http\Controllers\Admin\UserController::class, 'suspendAccount'])->name('suspend');
    Route::post('/{user}/activate', [\App\Http\Controllers\Admin\UserController::class, 'activateAccount'])->name('activate');

    // 2FA Management Routes
    Route::post('/{user}/enable-2fa', [\App\Http\Controllers\Admin\UserController::class, 'enableTwoFactor'])->name('enable-2fa');
    Route::post('/{user}/disable-2fa', [\App\Http\Controllers\Admin\UserController::class, 'disableTwoFactor'])->name('disable-2fa');
    Route::post('/{user}/reset-2fa', [\App\Http\Controllers\Admin\UserController::class, 'resetTwoFactor'])->name('reset-2fa');
});

// Soil Sensor Alert Routes are now handled in the main auth middleware group with coming.soon middleware

// Admin routes
Route::middleware([\App\Http\Middleware\AdminAccessControl::class, 'auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    // ... existing routes ...

    // Crop file upload route with security
    Route::post('/crops/upload', [CropController::class, 'uploadCropFile'])->middleware('secure.upload')->name('crops.upload');
    // Crop template download route
    Route::get('/crops/template', [CropController::class, 'downloadCropTemplate'])->name('crops.template');

    // Loan Payouts Management
    Route::prefix('loan-payouts')->name('loan-payouts.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\LoanPayoutController::class, 'index'])->name('index');
        Route::get('/{payout}', [\App\Http\Controllers\Admin\LoanPayoutController::class, 'show'])->name('show');
        Route::post('/{payout}/approve', [\App\Http\Controllers\Admin\LoanPayoutController::class, 'approve'])->name('approve');
        Route::post('/{payout}/reject', [\App\Http\Controllers\Admin\LoanPayoutController::class, 'reject'])->name('reject');
        Route::post('/{payout}/mark-paid', [\App\Http\Controllers\Admin\LoanPayoutController::class, 'markAsPaid'])->name('mark-paid');
        Route::post('/bulk-approve', [\App\Http\Controllers\Admin\LoanPayoutController::class, 'bulkApprove'])->name('bulk-approve');
        Route::get('/export/csv', [\App\Http\Controllers\Admin\LoanPayoutController::class, 'export'])->name('export');
    });
});

// WhatsApp webhook routes (no middleware for webhook verification)
Route::get('/whatsapp/webhook', [WhatsAppWebhookController::class, 'verify'])->name('whatsapp.webhook.verify');

// Temporary debug route for WhatsApp webhook
Route::post('/whatsapp/webhook', function(Request $request) {
    // Log everything for debugging
    \Log::info('WhatsApp webhook POST received', [
        'timestamp' => now()->toISOString(),
        'method' => $request->method(),
        'headers' => $request->headers->all(),
        'body' => $request->getContent(),
        'json' => $request->json()->all(),
        'all_data' => $request->all()
    ]);

    // Also call the debug script
    include_once(base_path('debug_whatsapp_webhook.php'));

    // Then call the actual controller
    $controller = app(\App\Http\Controllers\WhatsAppWebhookController::class);
    return $controller->handleWebhook($request);
})->name('whatsapp.webhook');

// Simple test endpoint for webhook verification
Route::get('/test-webhook', function(Request $request) {
    $mode = $request->query('hub_mode');
    $challenge = $request->query('hub_challenge');
    $token = $request->query('hub_verify_token');

    \Illuminate\Support\Facades\Log::info('Test webhook GET called', [
        'mode' => $mode,
        'challenge' => $challenge,
        'token' => $token,
        'expected_token' => 'riwe_webhook_verify_2024',
        'timestamp' => now()->toISOString(),
        'ip' => $request->ip(),
        'user_agent' => $request->userAgent()
    ]);

    if ($mode === 'subscribe' && $token === 'riwe_webhook_verify_2024') {
        return response($challenge)->header('Content-Type', 'text/plain');
    }

    return response('Verification failed', 403);
});

// Simplified WhatsApp webhook handler without dependency injection
Route::post('/test-webhook', function(Request $request) {
    try {
        \Illuminate\Support\Facades\Log::info('WhatsApp webhook POST received', [
            'timestamp' => now()->toISOString(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'payload' => $request->all(),
            'headers' => $request->headers->all()
        ]);

        $payload = $request->all();

        // Process the webhook payload
        if (isset($payload['entry'])) {
            foreach ($payload['entry'] as $entry) {
                if (isset($entry['changes'])) {
                    foreach ($entry['changes'] as $change) {
                        if ($change['field'] === 'messages' && isset($change['value']['messages'])) {
                            foreach ($change['value']['messages'] as $message) {
                                // Process each message
                                $phoneNumber = $message['from'] ?? null;
                                $messageText = $message['text']['body'] ?? null;
                                $messageType = $message['type'] ?? 'text';

                                if ($phoneNumber && $messageText) {
                                    \Illuminate\Support\Facades\Log::info('Processing WhatsApp message', [
                                        'from' => $phoneNumber,
                                        'text' => $messageText,
                                        'type' => $messageType
                                    ]);

                                    // Find or create user
                                    $cleanPhone = str_replace('whatsapp:', '', $phoneNumber);
                                    $cleanPhone = preg_replace('/[^0-9+]/', '', $cleanPhone);

                                    $user = \App\Models\User::where('phone_number', $cleanPhone)
                                        ->orWhere('phone_number', $phoneNumber)
                                        ->first();

                                    if (!$user) {
                                        // Create new WhatsApp guest user
                                        $userName = 'WhatsApp User ' . substr($cleanPhone, -4);

                                        // Get contact name if available
                                        if (isset($change['value']['contacts'][0]['profile']['name'])) {
                                            $userName = $change['value']['contacts'][0]['profile']['name'];
                                        }

                                        $user = \App\Models\User::create([
                                            'name' => $userName,
                                            'email' => 'whatsapp_' . $cleanPhone . '@guest.riwe.io',
                                            'phone_number' => $cleanPhone,
                                            'password' => bcrypt(\Illuminate\Support\Str::random(32)),
                                            'is_whatsapp_guest' => true,
                                            'email_verified_at' => now(),
                                            'preferred_currency' => 'NGN',
                                            'location' => 'Nigeria',
                                        ]);

                                        // Assign default role
                                        $defaultRole = \App\Models\Role::where('name', 'user')->first();
                                        if ($defaultRole) {
                                            $user->roles()->attach($defaultRole->id);
                                        }

                                        \Illuminate\Support\Facades\Log::info('Created new WhatsApp guest user', [
                                            'user_id' => $user->id,
                                            'phone' => $cleanPhone,
                                            'name' => $userName,
                                        ]);
                                    }

                                    // Process message through chatbot
                                    try {
                                        $chatbotService = app(\App\Services\ChatbotService::class);
                                        $response = $chatbotService->processMessage($user, $messageText);

                                        if ($response['success'] && isset($response['bot_message']['message'])) {
                                            $botMessage = $response['bot_message']['message'];

                                            // Send response back via WhatsApp
                                            $whatsappService = app(\App\Services\WhatsAppService::class);
                                            $sendResult = $whatsappService->sendMessage($phoneNumber, $botMessage);

                                            if ($sendResult['success']) {
                                                \Illuminate\Support\Facades\Log::info('WhatsApp response sent successfully', [
                                                    'phone' => $phoneNumber,
                                                    'message_id' => $sendResult['data']['messages'][0]['id'] ?? null,
                                                ]);
                                            } else {
                                                \Illuminate\Support\Facades\Log::error('Failed to send WhatsApp response', [
                                                    'phone' => $phoneNumber,
                                                    'error' => $sendResult['message'],
                                                ]);
                                            }
                                        }
                                    } catch (\Exception $e) {
                                        \Illuminate\Support\Facades\Log::error('Error processing chatbot message', [
                                            'error' => $e->getMessage(),
                                            'phone' => $phoneNumber,
                                        ]);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return response()->json(['status' => 'success', 'received' => true]);

    } catch (\Exception $e) {
        \Illuminate\Support\Facades\Log::error('WhatsApp webhook error', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'payload' => $request->all(),
        ]);

        return response()->json(['error' => 'Internal server error'], 500);
    }
});

// Simple webhook that just logs messages without processing
Route::post('/simple-webhook', function(Request $request) {
    \Illuminate\Support\Facades\Log::info('Simple webhook received message', [
        'timestamp' => now()->toISOString(),
        'ip' => $request->ip(),
        'user_agent' => $request->userAgent(),
        'payload' => $request->all()
    ]);

    $payload = $request->all();

    // Just extract and log the message without processing
    if (isset($payload['entry'])) {
        foreach ($payload['entry'] as $entry) {
            if (isset($entry['changes'])) {
                foreach ($entry['changes'] as $change) {
                    if ($change['field'] === 'messages' && isset($change['value']['messages'])) {
                        foreach ($change['value']['messages'] as $message) {
                            $phoneNumber = $message['from'] ?? null;
                            $messageText = $message['text']['body'] ?? null;

                            \Illuminate\Support\Facades\Log::info('WhatsApp message received', [
                                'from' => $phoneNumber,
                                'message' => $messageText,
                                'type' => $message['type'] ?? 'unknown'
                            ]);
                        }
                    }
                }
            }
        }
    }

    return response()->json(['status' => 'received', 'message' => 'Message logged successfully']);
});

// GET version for simple webhook (for verification)
Route::get('/simple-webhook', function(Request $request) {
    $mode = $request->query('hub_mode');
    $challenge = $request->query('hub_challenge');
    $token = $request->query('hub_verify_token');

    \Illuminate\Support\Facades\Log::info('Simple webhook verification', [
        'mode' => $mode,
        'challenge' => $challenge,
        'token' => $token
    ]);

    if ($mode === 'subscribe' && $token === 'riwe_webhook_verify_2024') {
        return response($challenge)->header('Content-Type', 'text/plain');
    }

    return response('Verification failed', 403);
});
