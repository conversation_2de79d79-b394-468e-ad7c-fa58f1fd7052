{{--
    Enhanced Vite Assets Component
    This component ensures that Vite assets are properly loaded in all environments.
    It handles both the @vite directive and direct asset loading with robust fallbacks.
--}}

@php
    $manifest = null;
    $manifestPath = public_path('build/manifest.json');
    $isLocal = app()->environment('local');
    $hotFileExists = file_exists(public_path('hot'));
    $assetsLoaded = false;

    // Try to load manifest
    if (file_exists($manifestPath)) {
        try {
            $manifestContent = file_get_contents($manifestPath);
            $manifest = json_decode($manifestContent, true);
        } catch (Exception $e) {
            \Log::warning('Failed to parse manifest.json', ['error' => $e->getMessage()]);
        }
    }

    // Default fallback files (updated to current build)
    $cssFile = 'assets/app-Bf4Uiskv.css';
    $jsFile = 'assets/app-D58IS0fg.js';

    // Try to get the actual files from manifest if available
    if ($manifest && is_array($manifest)) {
        $cssFile = $manifest['resources/css/app.css']['file'] ?? $cssFile;
        $jsFile = $manifest['resources/js/app.js']['file'] ?? $jsFile;
    }

    // Check if the files actually exist
    $cssPath = public_path('build/' . $cssFile);
    $jsPath = public_path('build/' . $jsFile);
    $cssExists = file_exists($cssPath);
    $jsExists = file_exists($jsPath);

    // Log asset loading status for debugging
    if (config('app.debug')) {
        \Log::info('Vite Assets Loading', [
            'manifest_exists' => file_exists($manifestPath),
            'css_file' => $cssFile,
            'js_file' => $jsFile,
            'css_exists' => $cssExists,
            'js_exists' => $jsExists,
            'css_path' => $cssPath,
            'js_path' => $jsPath
        ]);
    }
@endphp

{{-- Load built assets if they exist --}}
@if($cssExists)
    <link rel="stylesheet" href="{{ asset('build/' . $cssFile) }}?v={{ filemtime($cssPath) }}">
    @php $assetsLoaded = true; @endphp
@endif

@if($jsExists)
    <script type="module" src="{{ asset('build/' . $jsFile) }}?v={{ filemtime($jsPath) }}"></script>
    @php $assetsLoaded = true; @endphp
@endif

{{-- Fallback: Load Tailwind CSS from CDN if no CSS assets --}}
@if(!$cssExists)
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#036407',
                        secondary: '#F6A840',
                    }
                }
            }
        }
    </script>
    <style>
        /* Enhanced fallback styles */
        body {
            font-family: 'Figtree', sans-serif;
            background-color: #F5F3FF;
        }
        .sidenav {
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            background: white;
            z-index: 1040;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }
        .main-content {
            margin-left: 280px;
            padding: 20px;
            min-height: 100vh;
        }
        @media (max-width: 1024px) {
            .sidenav { transform: translateX(-100%); }
            .main-content { margin-left: 0; width: 100%; }
        }
    </style>
@endif

{{-- Emergency Fix CSS - Always load --}}
<link rel="stylesheet" href="{{ asset('css/emergency-fix.css') }}">

{{-- Asset loading status for debugging --}}
@if(config('app.debug'))
    <script>
        console.log('Vite Assets Status:', {
            cssLoaded: {{ $cssExists ? 'true' : 'false' }},
            jsLoaded: {{ $jsExists ? 'true' : 'false' }},
            cssFile: '{{ $cssFile }}',
            jsFile: '{{ $jsFile }}',
            manifestExists: {{ file_exists($manifestPath) ? 'true' : 'false' }}
        });
    </script>
@endif
