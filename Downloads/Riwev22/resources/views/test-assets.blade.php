<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Asset Loading Test') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h1 class="text-2xl font-bold mb-6">Laravel Asset Loading Test</h1>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- CSS Test -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h2 class="text-lg font-semibold mb-4">CSS Loading Test</h2>
                            
                            <!-- Tailwind Test -->
                            <div class="bg-blue-500 text-white p-4 rounded-lg shadow-md mb-4">
                                <p class="font-bold">✓ Tailwind CSS Test</p>
                                <p class="text-sm">If this box is blue with white text, <PERSON><PERSON><PERSON> is working!</p>
                            </div>
                            
                            <!-- Grid Test -->
                            <div class="grid grid-cols-2 gap-2 mb-4">
                                <div class="bg-green-100 p-2 rounded text-center">Grid 1</div>
                                <div class="bg-red-100 p-2 rounded text-center">Grid 2</div>
                            </div>
                            
                            <!-- Responsive Test -->
                            <div class="hidden md:block bg-yellow-100 p-2 rounded">
                                <p class="text-sm">This should only show on medium screens and up</p>
                            </div>
                            
                            <div id="css-status" class="mt-4 p-3 bg-gray-100 rounded">
                                <p class="font-semibold">CSS Status:</p>
                                <div id="css-results"></div>
                            </div>
                        </div>
                        
                        <!-- JavaScript Test -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h2 class="text-lg font-semibold mb-4">JavaScript Loading Test</h2>
                            
                            <!-- Alpine.js Test -->
                            <div x-data="{ count: 0, message: 'Alpine.js is loading...' }" class="mb-4">
                                <div class="bg-purple-100 p-3 rounded mb-2">
                                    <p x-text="message" class="font-semibold"></p>
                                    <p class="text-sm">Counter: <span x-text="count" class="font-bold"></span></p>
                                </div>
                                <button @click="count++; message = 'Alpine.js is working!'" 
                                        class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                                    Test Alpine.js
                                </button>
                            </div>
                            
                            <!-- AJAX Test -->
                            <div class="mb-4">
                                <button onclick="testAjax()" 
                                        class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600">
                                    Test AJAX (Axios)
                                </button>
                                <div id="ajax-result" class="mt-2 p-2 bg-gray-100 rounded text-sm"></div>
                            </div>
                            
                            <div id="js-status" class="mt-4 p-3 bg-gray-100 rounded">
                                <p class="font-semibold">JavaScript Status:</p>
                                <div id="js-results"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Asset Information -->
                    <div class="mt-8 bg-gray-50 p-4 rounded-lg">
                        <h2 class="text-lg font-semibold mb-4">Asset Information</h2>
                        <div id="asset-info" class="space-y-2"></div>
                    </div>
                    
                    <!-- Console Logs -->
                    <div class="mt-8 bg-gray-50 p-4 rounded-lg">
                        <h2 class="text-lg font-semibold mb-4">Console Logs</h2>
                        <div id="console-output" class="bg-black text-green-400 p-3 rounded font-mono text-sm h-32 overflow-y-auto"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Console log capture
        const logs = [];
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addLog(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${type.toUpperCase()}: ${args.join(' ')}`);
            updateConsoleOutput();
        }
        
        console.log = (...args) => {
            originalLog(...args);
            addLog('log', ...args);
        };
        
        console.error = (...args) => {
            originalError(...args);
            addLog('error', ...args);
        };
        
        console.warn = (...args) => {
            originalWarn(...args);
            addLog('warn', ...args);
        };
        
        function updateConsoleOutput() {
            const output = document.getElementById('console-output');
            if (output) {
                output.innerHTML = logs.slice(-20).join('<br>');
                output.scrollTop = output.scrollHeight;
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Asset test page loaded');
            
            // Test CSS
            testCSS();
            
            // Test JavaScript
            setTimeout(testJavaScript, 500);
            
            // Load asset information
            loadAssetInfo();
        });
        
        function testCSS() {
            const results = document.getElementById('css-results');
            
            // Test Tailwind
            const testEl = document.createElement('div');
            testEl.className = 'bg-red-500 text-white p-2';
            document.body.appendChild(testEl);
            
            const styles = window.getComputedStyle(testEl);
            const bgColor = styles.backgroundColor;
            
            if (bgColor.includes('239') || bgColor.includes('rgb(239, 68, 68)')) {
                results.innerHTML += '<div class="text-green-600">✓ Tailwind CSS working</div>';
            } else {
                results.innerHTML += '<div class="text-red-600">✗ Tailwind CSS not working</div>';
            }
            
            document.body.removeChild(testEl);
            
            // Count stylesheets
            const stylesheets = document.styleSheets.length;
            results.innerHTML += `<div class="text-blue-600">Stylesheets loaded: ${stylesheets}</div>`;
        }
        
        function testJavaScript() {
            const results = document.getElementById('js-results');
            
            // Test Alpine.js
            if (typeof window.Alpine !== 'undefined') {
                results.innerHTML += '<div class="text-green-600">✓ Alpine.js loaded</div>';
            } else {
                results.innerHTML += '<div class="text-red-600">✗ Alpine.js not loaded</div>';
            }
            
            // Test Axios
            if (typeof window.axios !== 'undefined') {
                results.innerHTML += '<div class="text-green-600">✓ Axios loaded</div>';
            } else {
                results.innerHTML += '<div class="text-red-600">✗ Axios not loaded</div>';
            }
        }
        
        function testAjax() {
            const result = document.getElementById('ajax-result');
            result.innerHTML = 'Testing AJAX...';
            
            if (typeof window.axios !== 'undefined') {
                window.axios.get('/csrf-token')
                    .then(response => {
                        result.innerHTML = '<span class="text-green-600">✓ AJAX working! CSRF token received.</span>';
                        console.log('AJAX test successful');
                    })
                    .catch(error => {
                        result.innerHTML = '<span class="text-red-600">✗ AJAX failed: ' + error.message + '</span>';
                        console.error('AJAX test failed:', error);
                    });
            } else {
                result.innerHTML = '<span class="text-red-600">✗ Axios not available</span>';
            }
        }
        
        function loadAssetInfo() {
            const info = document.getElementById('asset-info');
            
            // Get manifest info
            fetch('/build/manifest.json')
                .then(response => response.json())
                .then(manifest => {
                    info.innerHTML += `<div><strong>CSS File:</strong> ${manifest['resources/css/app.css'].file}</div>`;
                    info.innerHTML += `<div><strong>JS File:</strong> ${manifest['resources/js/app.js'].file}</div>`;
                })
                .catch(error => {
                    info.innerHTML += `<div class="text-red-600">Error loading manifest: ${error.message}</div>`;
                });
            
            // List loaded stylesheets
            const stylesheets = Array.from(document.styleSheets);
            info.innerHTML += `<div><strong>Loaded Stylesheets:</strong></div>`;
            stylesheets.forEach((sheet, index) => {
                const href = sheet.href || 'inline';
                info.innerHTML += `<div class="ml-4 text-sm">${index + 1}. ${href}</div>`;
            });
        }
        
        // Error handling
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
        });
    </script>
    @endpush
</x-app-layout>
