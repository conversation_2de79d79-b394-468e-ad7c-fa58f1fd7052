import Web3 from 'web3';
import WalletConnectProvider from '@walletconnect/web3-provider';

class RiweWeb3 {
    constructor() {
        this.web3 = null;
        this.provider = null;
        this.account = null;
        this.chainId = null;
        this.isConnected = false;
        this.contracts = {};
        
        // Network configurations
        this.networks = {
            polygon: {
                chainId: '0x89', // 137
                chainName: 'Polygon Mainnet',
                nativeCurrency: {
                    name: 'MATI<PERSON>',
                    symbol: 'MATIC',
                    decimals: 18
                },
                rpcUrls: ['https://polygon-rpc.com'],
                blockExplorerUrls: ['https://polygonscan.com']
            },
            polygon_testnet: {
                chainId: '0x13881', // 80001
                chainName: 'Polygon Mumbai',
                nativeCurrency: {
                    name: 'MATI<PERSON>',
                    symbol: 'MATIC',
                    decimals: 18
                },
                rpcUrls: ['https://rpc-mumbai.maticvigil.com'],
                blockExplorerUrls: ['https://mumbai.polygonscan.com']
            }
        };
        
        this.init();
    }
    
    async init() {
        // Check if already connected
        if (window.ethereum) {
            this.web3 = new Web3(window.ethereum);
            
            // Check if already connected
            const accounts = await window.ethereum.request({ method: 'eth_accounts' });
            if (accounts.length > 0) {
                this.account = accounts[0];
                this.isConnected = true;
                this.chainId = await window.ethereum.request({ method: 'eth_chainId' });
                this.setupEventListeners();
            }
        }
    }
    
    setupEventListeners() {
        if (window.ethereum) {
            window.ethereum.on('accountsChanged', (accounts) => {
                if (accounts.length === 0) {
                    this.disconnect();
                } else {
                    this.account = accounts[0];
                    this.onAccountChanged(accounts[0]);
                }
            });
            
            window.ethereum.on('chainChanged', (chainId) => {
                this.chainId = chainId;
                this.onChainChanged(chainId);
            });
            
            window.ethereum.on('disconnect', () => {
                this.disconnect();
            });
        }
    }
    
    async connectMetaMask() {
        if (!window.ethereum) {
            throw new Error('MetaMask is not installed');
        }
        
        try {
            const accounts = await window.ethereum.request({
                method: 'eth_requestAccounts'
            });
            
            this.web3 = new Web3(window.ethereum);
            this.provider = window.ethereum;
            this.account = accounts[0];
            this.chainId = await window.ethereum.request({ method: 'eth_chainId' });
            this.isConnected = true;
            
            this.setupEventListeners();
            
            // Verify signature for backend
            const message = `Connect wallet to Riwe platform at ${Date.now()}`;
            const signature = await this.signMessage(message);
            
            // Send to backend
            await this.verifyConnection(this.account, signature, message);
            
            return {
                account: this.account,
                chainId: this.chainId
            };
        } catch (error) {
            console.error('Failed to connect MetaMask:', error);
            throw error;
        }
    }
    
    async connectWalletConnect() {
        try {
            const provider = new WalletConnectProvider({
                rpc: {
                    137: 'https://polygon-rpc.com',
                    80001: 'https://rpc-mumbai.maticvigil.com'
                }
            });
            
            await provider.enable();
            
            this.web3 = new Web3(provider);
            this.provider = provider;
            this.account = provider.accounts[0];
            this.chainId = provider.chainId;
            this.isConnected = true;
            
            // Verify signature for backend
            const message = `Connect wallet to Riwe platform at ${Date.now()}`;
            const signature = await this.signMessage(message);
            
            // Send to backend
            await this.verifyConnection(this.account, signature, message);
            
            return {
                account: this.account,
                chainId: this.chainId
            };
        } catch (error) {
            console.error('Failed to connect WalletConnect:', error);
            throw error;
        }
    }
    
    async verifyConnection(walletAddress, signature, message) {
        const csrfToken = this.getCSRFToken();
        if (!csrfToken) {
            console.warn('CSRF token not found, skipping backend verification');
            return { success: true, message: 'Frontend connection only' };
        }

        const response = await fetch('/web3/connect-wallet', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({
                wallet_address: walletAddress,
                signature: signature,
                message: message
            })
        });
        
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message || 'Failed to verify wallet connection');
        }
        
        return data;
    }
    
    async disconnect() {
        if (this.provider && this.provider.disconnect) {
            await this.provider.disconnect();
        }
        
        this.web3 = null;
        this.provider = null;
        this.account = null;
        this.chainId = null;
        this.isConnected = false;
        this.contracts = {};
        
        // Notify backend
        const csrfToken = this.getCSRFToken();
        if (csrfToken) {
            await fetch('/web3/disconnect-wallet', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                }
            });
        }
        
        this.onDisconnected();
    }
    
    async switchNetwork(networkName) {
        if (!this.isConnected) {
            throw new Error('Wallet not connected');
        }
        
        const network = this.networks[networkName];
        if (!network) {
            throw new Error('Unsupported network');
        }
        
        try {
            await window.ethereum.request({
                method: 'wallet_switchEthereumChain',
                params: [{ chainId: network.chainId }]
            });
        } catch (switchError) {
            // This error code indicates that the chain has not been added to MetaMask
            if (switchError.code === 4902) {
                try {
                    await window.ethereum.request({
                        method: 'wallet_addEthereumChain',
                        params: [network]
                    });
                } catch (addError) {
                    throw addError;
                }
            } else {
                throw switchError;
            }
        }
    }
    
    async signMessage(message) {
        if (!this.isConnected) {
            throw new Error('Wallet not connected');
        }
        
        return await this.web3.eth.personal.sign(message, this.account);
    }
    
    async getBalance(address = null) {
        if (!this.web3) {
            throw new Error('Web3 not initialized');
        }
        
        const targetAddress = address || this.account;
        const balance = await this.web3.eth.getBalance(targetAddress);
        return this.web3.utils.fromWei(balance, 'ether');
    }
    
    async getTokenBalance(tokenAddress, address = null) {
        if (!this.web3) {
            throw new Error('Web3 not initialized');
        }
        
        const targetAddress = address || this.account;
        
        // ERC-20 ABI for balanceOf function
        const minABI = [
            {
                constant: true,
                inputs: [{ name: "_owner", type: "address" }],
                name: "balanceOf",
                outputs: [{ name: "balance", type: "uint256" }],
                type: "function"
            },
            {
                constant: true,
                inputs: [],
                name: "decimals",
                outputs: [{ name: "", type: "uint8" }],
                type: "function"
            }
        ];
        
        const contract = new this.web3.eth.Contract(minABI, tokenAddress);
        const balance = await contract.methods.balanceOf(targetAddress).call();
        const decimals = await contract.methods.decimals().call();
        
        return this.web3.utils.fromWei(balance, 'ether');
    }
    
    async sendTransaction(to, value, data = '0x') {
        if (!this.isConnected) {
            throw new Error('Wallet not connected');
        }
        
        const gasPrice = await this.web3.eth.getGasPrice();
        const gasLimit = await this.web3.eth.estimateGas({
            from: this.account,
            to: to,
            value: value,
            data: data
        });
        
        return await this.web3.eth.sendTransaction({
            from: this.account,
            to: to,
            value: value,
            data: data,
            gas: gasLimit,
            gasPrice: gasPrice
        });
    }
    
    async transferTokens(tokenAddress, to, amount) {
        if (!this.isConnected) {
            throw new Error('Wallet not connected');
        }
        
        // ERC-20 ABI for transfer function
        const minABI = [
            {
                constant: false,
                inputs: [
                    { name: "_to", type: "address" },
                    { name: "_value", type: "uint256" }
                ],
                name: "transfer",
                outputs: [{ name: "", type: "bool" }],
                type: "function"
            }
        ];
        
        const contract = new this.web3.eth.Contract(minABI, tokenAddress);
        const amountWei = this.web3.utils.toWei(amount.toString(), 'ether');
        
        return await contract.methods.transfer(to, amountWei).send({
            from: this.account
        });
    }
    
    async stakeTokens(stakingAddress, amount) {
        if (!this.isConnected) {
            throw new Error('Wallet not connected');
        }
        
        // Staking contract ABI for stake function
        const stakingABI = [
            {
                inputs: [{ name: "amount", type: "uint256" }],
                name: "stake",
                outputs: [],
                stateMutability: "nonpayable",
                type: "function"
            }
        ];
        
        const contract = new this.web3.eth.Contract(stakingABI, stakingAddress);
        const amountWei = this.web3.utils.toWei(amount.toString(), 'ether');
        
        return await contract.methods.stake(amountWei).send({
            from: this.account
        });
    }
    
    // Event handlers (to be overridden)
    onAccountChanged(account) {
        console.log('Account changed:', account);
        // Reload page or update UI
        window.location.reload();
    }
    
    onChainChanged(chainId) {
        console.log('Chain changed:', chainId);
        // Reload page or update UI
        window.location.reload();
    }
    
    onDisconnected() {
        console.log('Wallet disconnected');
        // Redirect to login or update UI
        window.location.reload();
    }
    
    // Utility functions
    getCSRFToken() {
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        return metaTag ? metaTag.getAttribute('content') : null;
    }

    formatAddress(address) {
        if (!address) return '';
        return `${address.slice(0, 6)}...${address.slice(-4)}`;
    }
    
    formatBalance(balance, decimals = 4) {
        return parseFloat(balance).toFixed(decimals);
    }
    
    isValidAddress(address) {
        return this.web3 ? this.web3.utils.isAddress(address) : false;
    }
    
    toWei(amount) {
        return this.web3 ? this.web3.utils.toWei(amount.toString(), 'ether') : '0';
    }
    
    fromWei(amount) {
        return this.web3 ? this.web3.utils.fromWei(amount.toString(), 'ether') : '0';
    }
}

// Global instance
window.RiweWeb3 = new RiweWeb3();

// Export for module usage
export default RiweWeb3;
